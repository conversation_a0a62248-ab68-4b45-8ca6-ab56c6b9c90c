// ===== MAIN APPLICATION JAVASCRIPT =====

// Global App Object
const ENGAZ = {
    // Configuration
    config: {
        animationDuration: 300,
        scrollOffset: 100,
        debounceDelay: 250,
        apiEndpoint: '/api',
        version: '1.0.0'
    },
    
    // State management
    state: {
        isLoading: true,
        currentLanguage: 'ar',
        isMenuOpen: false,
        scrollPosition: 0,
        activeSection: 'home'
    },
    
    // Cache DOM elements
    elements: {},
    
    // Initialize application
    init() {
        this.cacheElements();
        this.bindEvents();
        this.initializeComponents();
        this.handleInitialLoad();
    },
    
    // Cache frequently used DOM elements
    cacheElements() {
        this.elements = {
            // Loading
            loadingScreen: document.getElementById('loading-screen'),
            
            // Navigation
            header: document.getElementById('header'),
            navToggle: document.getElementById('nav-toggle'),
            navMenu: document.getElementById('nav-menu'),
            navLinks: document.querySelectorAll('.nav-link'),
            languageToggle: document.getElementById('language-toggle'),
            currentLang: document.getElementById('current-lang'),
            
            // Back to top
            backToTop: document.getElementById('back-to-top'),
            
            // Sections
            sections: document.querySelectorAll('section[id]'),
            
            // Statistics
            statNumbers: document.querySelectorAll('.stat-number'),
            
            // Forms
            forms: document.querySelectorAll('form'),
            formInputs: document.querySelectorAll('.form-input, .form-select, .form-textarea'),
            
            // Modals
            modals: document.querySelectorAll('.modal'),
            modalTriggers: document.querySelectorAll('[data-modal]'),
            modalCloses: document.querySelectorAll('.modal-close'),
            
            // Dropdowns
            dropdowns: document.querySelectorAll('.dropdown'),
            
            // Accordions
            accordions: document.querySelectorAll('.accordion-item'),
            
            // Tabs
            tabButtons: document.querySelectorAll('.tab-button'),
            tabPanels: document.querySelectorAll('.tab-panel')
        };
    },
    
    // Bind event listeners
    bindEvents() {
        // Window events
        window.addEventListener('load', () => this.handleWindowLoad());
        window.addEventListener('scroll', this.debounce(() => this.handleScroll(), this.config.debounceDelay));
        window.addEventListener('resize', this.debounce(() => this.handleResize(), this.config.debounceDelay));
        
        // Navigation events
        if (this.elements.navToggle) {
            this.elements.navToggle.addEventListener('click', () => this.toggleMobileMenu());
        }
        
        if (this.elements.languageToggle) {
            this.elements.languageToggle.addEventListener('click', () => this.toggleLanguage());
        }
        
        // Navigation links
        this.elements.navLinks.forEach(link => {
            link.addEventListener('click', (e) => this.handleNavClick(e));
        });
        
        // Back to top button
        if (this.elements.backToTop) {
            this.elements.backToTop.addEventListener('click', () => this.scrollToTop());
        }
        
        // Form events
        this.elements.forms.forEach(form => {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        });
        
        this.elements.formInputs.forEach(input => {
            input.addEventListener('blur', (e) => this.validateField(e.target));
            input.addEventListener('input', (e) => this.clearFieldError(e.target));
        });
        
        // Modal events
        this.elements.modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => this.openModal(e));
        });
        
        this.elements.modalCloses.forEach(close => {
            close.addEventListener('click', (e) => this.closeModal(e));
        });
        
        // Close modal on backdrop click
        this.elements.modals.forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) this.closeModal(e);
            });
        });
        
        // Dropdown events
        this.elements.dropdowns.forEach(dropdown => {
            const toggle = dropdown.querySelector('.dropdown-toggle');
            if (toggle) {
                toggle.addEventListener('click', () => this.toggleDropdown(dropdown));
            }
        });
        
        // Accordion events
        this.elements.accordions.forEach(accordion => {
            const header = accordion.querySelector('.accordion-header');
            if (header) {
                header.addEventListener('click', () => this.toggleAccordion(accordion));
            }
        });
        
        // Tab events
        this.elements.tabButtons.forEach(button => {
            button.addEventListener('click', (e) => this.switchTab(e));
        });
        
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeydown(e));
        
        // Click outside to close dropdowns
        document.addEventListener('click', (e) => this.handleOutsideClick(e));
    },
    
    // Initialize components
    initializeComponents() {
        this.initScrollReveal();
        this.initCounters();
        this.initSmoothScroll();
        this.loadLanguage();
    },
    
    // Handle initial page load
    handleInitialLoad() {
        // Set initial language
        const savedLanguage = localStorage.getItem('engaz-language') || 'ar';
        this.setLanguage(savedLanguage);
        
        // Set initial theme
        const savedTheme = localStorage.getItem('engaz-theme') || 'light';
        this.setTheme(savedTheme);
        
        // Initialize active section
        this.updateActiveSection();
    },
    
    // Handle window load event
    handleWindowLoad() {
        setTimeout(() => {
            this.hideLoadingScreen();
            this.initAnimations();
        }, 1000);
    },
    
    // Hide loading screen
    hideLoadingScreen() {
        if (this.elements.loadingScreen) {
            this.elements.loadingScreen.classList.add('hidden');
            this.state.isLoading = false;
        }
    },
    
    // Handle scroll events
    handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        this.state.scrollPosition = scrollTop;
        
        // Update header appearance
        this.updateHeaderOnScroll(scrollTop);
        
        // Update back to top button
        this.updateBackToTop(scrollTop);
        
        // Update active navigation
        this.updateActiveSection();
        
        // Trigger scroll animations
        this.handleScrollAnimations();
    },
    
    // Update header on scroll
    updateHeaderOnScroll(scrollTop) {
        if (this.elements.header) {
            if (scrollTop > this.config.scrollOffset) {
                this.elements.header.classList.add('scrolled');
            } else {
                this.elements.header.classList.remove('scrolled');
            }
        }
    },
    
    // Update back to top button
    updateBackToTop(scrollTop) {
        if (this.elements.backToTop) {
            if (scrollTop > this.config.scrollOffset * 3) {
                this.elements.backToTop.classList.add('visible');
            } else {
                this.elements.backToTop.classList.remove('visible');
            }
        }
    },
    
    // Update active navigation section
    updateActiveSection() {
        let current = '';
        
        this.elements.sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            
            if (this.state.scrollPosition >= sectionTop - this.config.scrollOffset) {
                current = section.getAttribute('id');
            }
        });
        
        if (current !== this.state.activeSection) {
            this.state.activeSection = current;
            this.updateActiveNavLink(current);
        }
    },
    
    // Update active navigation link
    updateActiveNavLink(activeSection) {
        this.elements.navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${activeSection}`) {
                link.classList.add('active');
            }
        });
    },
    
    // Handle window resize
    handleResize() {
        // Close mobile menu on desktop
        if (window.innerWidth >= 1024 && this.state.isMenuOpen) {
            this.closeMobileMenu();
        }
        
        // Recalculate scroll positions
        this.updateActiveSection();
    },
    
    // Toggle mobile menu
    toggleMobileMenu() {
        this.state.isMenuOpen = !this.state.isMenuOpen;
        
        if (this.state.isMenuOpen) {
            this.openMobileMenu();
        } else {
            this.closeMobileMenu();
        }
    },
    
    // Open mobile menu
    openMobileMenu() {
        this.elements.navMenu.classList.add('active');
        this.elements.navToggle.classList.add('active');
        document.body.style.overflow = 'hidden';
    },
    
    // Close mobile menu
    closeMobileMenu() {
        this.elements.navMenu.classList.remove('active');
        this.elements.navToggle.classList.remove('active');
        document.body.style.overflow = '';
        this.state.isMenuOpen = false;
    },
    
    // Handle navigation link clicks
    handleNavClick(e) {
        const href = e.target.getAttribute('href');
        
        if (href && href.startsWith('#')) {
            e.preventDefault();
            const targetId = href.substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                this.scrollToElement(targetElement);
                this.closeMobileMenu();
            }
        }
    },
    
    // Scroll to element smoothly
    scrollToElement(element) {
        const headerHeight = this.elements.header ? this.elements.header.offsetHeight : 0;
        const targetPosition = element.offsetTop - headerHeight - 20;
        
        window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
        });
    },
    
    // Scroll to top
    scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    },
    
    // Toggle language
    toggleLanguage() {
        const newLanguage = this.state.currentLanguage === 'ar' ? 'en' : 'ar';
        this.setLanguage(newLanguage);
    },
    
    // Set language
    setLanguage(language) {
        this.state.currentLanguage = language;
        
        // Update HTML attributes
        document.documentElement.lang = language;
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
        
        // Update language toggle button
        if (this.elements.currentLang) {
            this.elements.currentLang.textContent = language === 'ar' ? 'EN' : 'ع';
        }
        
        // Save to localStorage
        localStorage.setItem('engaz-language', language);
        
        // Update translations
        this.updateTranslations();
    },
    
    // Load language data
    loadLanguage() {
        // This would typically load from a JSON file
        // For now, we'll use the language.js file
        if (typeof window.ENGAZ_TRANSLATIONS !== 'undefined') {
            this.translations = window.ENGAZ_TRANSLATIONS;
        }
    },
    
    // Update translations
    updateTranslations() {
        if (!this.translations) return;
        
        const elements = document.querySelectorAll('[data-translate]');
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            
            if (translation) {
                if (element.tagName === 'INPUT' && element.type === 'submit') {
                    element.value = translation;
                } else if (element.hasAttribute('placeholder')) {
                    element.placeholder = translation;
                } else {
                    element.textContent = translation;
                }
            }
        });
    },
    
    // Get translation for key
    getTranslation(key) {
        if (!this.translations || !this.translations[this.state.currentLanguage]) {
            return null;
        }
        
        return this.translations[this.state.currentLanguage][key] || null;
    },
    
    // Initialize scroll reveal animations
    initScrollReveal() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, observerOptions);
        
        // Observe elements with scroll reveal classes
        const revealElements = document.querySelectorAll('.scroll-reveal, .scroll-reveal-left, .scroll-reveal-right, .scroll-reveal-scale');
        revealElements.forEach(element => {
            observer.observe(element);
        });
    },
    
    // Initialize counter animations
    initCounters() {
        const counterElements = this.elements.statNumbers;
        
        const observerOptions = {
            threshold: 0.5
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        counterElements.forEach(element => {
            observer.observe(element);
        });
    },
    
    // Animate counter
    animateCounter(element) {
        const target = parseInt(element.getAttribute('data-count'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            element.textContent = Math.floor(current);
            
            if (current >= target) {
                element.textContent = target;
                clearInterval(timer);
            }
        }, 16);
    },
    
    // Initialize smooth scroll
    initSmoothScroll() {
        // Already handled in handleNavClick
    },
    
    // Initialize animations
    initAnimations() {
        // Add stagger animation to service cards
        const serviceCards = document.querySelectorAll('.service-card');
        serviceCards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('animate-fade-in-up');
            }, index * 100);
        });
    },
    
    // Handle scroll animations
    handleScrollAnimations() {
        // This is handled by the Intersection Observer in initScrollReveal
    },
    
    // Utility: Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Utility: Throttle function
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Handle form submission
    handleFormSubmit(e) {
        e.preventDefault();
        const form = e.target;

        if (this.validateForm(form)) {
            this.submitForm(form);
        }
    },

    // Validate entire form
    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('.form-input, .form-select, .form-textarea');

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    },

    // Validate individual field
    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');
        let isValid = true;
        let errorMessage = '';

        // Clear previous errors
        this.clearFieldError(field);

        // Required field validation
        if (required && !value) {
            isValid = false;
            errorMessage = this.getTranslation('field_required') || 'هذا الحقل مطلوب';
        }

        // Email validation
        else if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = this.getTranslation('invalid_email') || 'البريد الإلكتروني غير صحيح';
            }
        }

        // Phone validation
        else if (type === 'tel' && value) {
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (!phoneRegex.test(value.replace(/\s/g, ''))) {
                isValid = false;
                errorMessage = this.getTranslation('invalid_phone') || 'رقم الهاتف غير صحيح';
            }
        }

        // Password validation
        else if (type === 'password' && value) {
            if (value.length < 8) {
                isValid = false;
                errorMessage = this.getTranslation('password_min_length') || 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
            }
        }

        if (!isValid) {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    },

    // Show field error
    showFieldError(field, message) {
        field.classList.add('error');

        let errorElement = field.parentNode.querySelector('.form-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'form-error';
            field.parentNode.appendChild(errorElement);
        }

        errorElement.textContent = message;
    },

    // Clear field error
    clearFieldError(field) {
        field.classList.remove('error');
        const errorElement = field.parentNode.querySelector('.form-error');
        if (errorElement) {
            errorElement.remove();
        }
    },

    // Submit form
    async submitForm(form) {
        const formData = new FormData(form);
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');

        // Show loading state
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.classList.add('loading');
        }

        try {
            // Simulate API call
            await this.delay(2000);

            // Show success message
            this.showAlert('success', this.getTranslation('form_success') || 'تم إرسال النموذج بنجاح');
            form.reset();

        } catch (error) {
            // Show error message
            this.showAlert('error', this.getTranslation('form_error') || 'حدث خطأ أثناء إرسال النموذج');
        } finally {
            // Reset button state
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.classList.remove('loading');
            }
        }
    },

    // Show alert message
    showAlert(type, message) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            <span>${message}</span>
        `;

        // Insert at top of page
        document.body.insertBefore(alert, document.body.firstChild);

        // Auto remove after 5 seconds
        setTimeout(() => {
            alert.remove();
        }, 5000);
    },

    // Modal functions
    openModal(e) {
        e.preventDefault();
        const modalId = e.target.getAttribute('data-modal');
        const modal = document.getElementById(modalId);

        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    },

    closeModal(e) {
        const modal = e.target.closest('.modal');
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    },

    // Dropdown functions
    toggleDropdown(dropdown) {
        const isActive = dropdown.classList.contains('active');

        // Close all dropdowns
        this.elements.dropdowns.forEach(d => d.classList.remove('active'));

        // Toggle current dropdown
        if (!isActive) {
            dropdown.classList.add('active');
        }
    },

    // Accordion functions
    toggleAccordion(accordion) {
        const isActive = accordion.classList.contains('active');

        // Close all accordions in the same group
        const group = accordion.closest('.accordion-group');
        if (group) {
            group.querySelectorAll('.accordion-item').forEach(item => {
                item.classList.remove('active');
            });
        }

        // Toggle current accordion
        if (!isActive) {
            accordion.classList.add('active');
        }
    },

    // Tab functions
    switchTab(e) {
        e.preventDefault();
        const button = e.target;
        const tabGroup = button.closest('.tabs');
        const targetId = button.getAttribute('data-tab');

        if (!tabGroup || !targetId) return;

        // Update buttons
        tabGroup.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        button.classList.add('active');

        // Update panels
        tabGroup.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.remove('active');
        });

        const targetPanel = document.getElementById(targetId);
        if (targetPanel) {
            targetPanel.classList.add('active');
        }
    },

    // Handle keyboard events
    handleKeydown(e) {
        // Escape key closes modals and dropdowns
        if (e.key === 'Escape') {
            // Close modals
            this.elements.modals.forEach(modal => {
                if (modal.classList.contains('active')) {
                    modal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });

            // Close dropdowns
            this.elements.dropdowns.forEach(dropdown => {
                dropdown.classList.remove('active');
            });

            // Close mobile menu
            if (this.state.isMenuOpen) {
                this.closeMobileMenu();
            }
        }
    },

    // Handle clicks outside elements
    handleOutsideClick(e) {
        // Close dropdowns when clicking outside
        this.elements.dropdowns.forEach(dropdown => {
            if (!dropdown.contains(e.target)) {
                dropdown.classList.remove('active');
            }
        });
    },

    // Set theme
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('engaz-theme', theme);
    },

    // Utility: Delay function
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
};

// Initialize app when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => ENGAZ.init());
} else {
    ENGAZ.init();
}

// Export for use in other modules
window.ENGAZ = ENGAZ;
