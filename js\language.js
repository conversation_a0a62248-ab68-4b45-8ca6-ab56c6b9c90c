// ===== MULTI-LANGUAGE SUPPORT =====

window.ENGAZ_TRANSLATIONS = {
    ar: {
        // Navigation
        home: 'الرئيسية',
        services: 'خدماتنا',
        about: 'من نحن',
        contact: 'اتصل بنا',
        login: 'تسجيل الدخول',
        register: 'إنشاء حساب',
        dashboard: 'لوحة التحكم',
        
        // Hero Section
        hero_title: 'رحلتك تبدأ من هنا مع إنجز',
        hero_subtitle: 'منصة شاملة لجميع خدمات السفر للمصريين المسافرين للخارج من معالجة الوثائق إلى التخطيط الكامل لرحلتك',
        start_journey: 'ابدأ رحلتك الآن',
        explore_services: 'استكشف خدماتنا',
        
        // Services
        our_services: 'خدماتنا',
        services_subtitle: 'نقدم مجموعة شاملة من الخدمات لجعل سفرك أسهل وأكثر راحة',
        document_processing: 'معالجة الوثائق',
        document_processing_desc: 'نساعدك في إعداد وتجهيز جميع الوثائق المطلوبة للسفر',
        visa_assistance: 'مساعدة التأشيرة',
        visa_assistance_desc: 'إرشاد شامل لعملية التقديم للحصول على التأشيرة',
        travel_planning: 'تخطيط السفر',
        travel_planning_desc: 'تخطيط شامل لرحلتك من الطيران إلى الإقامة والأنشطة',
        status_tracking: 'تتبع الحالة',
        status_tracking_desc: 'تابع حالة طلباتك وخدماتك في الوقت الفعلي',
        learn_more: 'اعرف المزيد',
        
        // Features
        why_choose_us: 'لماذا تختار إنجز؟',
        secure_reliable: 'آمن وموثوق',
        secure_reliable_desc: 'حماية كاملة لبياناتك الشخصية ومعلوماتك الحساسة',
        fast_efficient: 'سريع وفعال',
        fast_efficient_desc: 'معالجة سريعة لطلباتك مع متابعة مستمرة',
        expert_support: 'دعم متخصص',
        expert_support_desc: 'فريق من الخبراء متاح لمساعدتك على مدار الساعة',
        
        // Statistics
        happy_customers: 'عميل سعيد',
        countries_served: 'دولة نخدمها',
        success_rate: 'معدل النجاح %',
        support_hours: 'ساعة دعم',
        
        // CTA
        ready_to_travel: 'مستعد للسفر؟',
        start_your_journey: 'ابدأ رحلتك معنا اليوم واحصل على أفضل خدمات السفر',
        get_started: 'ابدأ الآن',
        
        // Footer
        footer_description: 'منصة شاملة لخدمات السفر للمصريين المسافرين للخارج',
        quick_links: 'روابط سريعة',
        contact_info: 'معلومات الاتصال',
        follow_us: 'تابعنا',
        all_rights_reserved: 'جميع الحقوق محفوظة',
        
        // Forms
        field_required: 'هذا الحقل مطلوب',
        invalid_email: 'البريد الإلكتروني غير صحيح',
        invalid_phone: 'رقم الهاتف غير صحيح',
        password_min_length: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
        form_success: 'تم إرسال النموذج بنجاح',
        form_error: 'حدث خطأ أثناء إرسال النموذج',
        
        // Common
        loading: 'جاري التحميل...',
        submit: 'إرسال',
        cancel: 'إلغاء',
        close: 'إغلاق',
        save: 'حفظ',
        edit: 'تعديل',
        delete: 'حذف',
        confirm: 'تأكيد',
        yes: 'نعم',
        no: 'لا',
        
        // Login/Register
        email: 'البريد الإلكتروني',
        password: 'كلمة المرور',
        confirm_password: 'تأكيد كلمة المرور',
        full_name: 'الاسم الكامل',
        phone: 'رقم الهاتف',
        remember_me: 'تذكرني',
        forgot_password: 'نسيت كلمة المرور؟',
        create_account: 'إنشاء حساب جديد',
        already_have_account: 'لديك حساب بالفعل؟',
        dont_have_account: 'ليس لديك حساب؟',
        
        // Dashboard
        welcome_back: 'مرحباً بعودتك',
        my_applications: 'طلباتي',
        my_documents: 'وثائقي',
        profile_settings: 'إعدادات الملف الشخصي',
        logout: 'تسجيل الخروج',
        
        // Document Processing
        upload_document: 'رفع وثيقة',
        document_type: 'نوع الوثيقة',
        passport: 'جواز السفر',
        national_id: 'البطاقة الشخصية',
        birth_certificate: 'شهادة الميلاد',
        marriage_certificate: 'شهادة الزواج',
        bank_statement: 'كشف حساب بنكي',
        employment_letter: 'خطاب عمل',
        
        // Visa Assistance
        destination_country: 'دولة الوجهة',
        visa_type: 'نوع التأشيرة',
        tourist_visa: 'تأشيرة سياحية',
        business_visa: 'تأشيرة عمل',
        student_visa: 'تأشيرة دراسة',
        family_visa: 'تأشيرة عائلية',
        travel_date: 'تاريخ السفر',
        return_date: 'تاريخ العودة',
        
        // Travel Planning
        flight_booking: 'حجز الطيران',
        hotel_booking: 'حجز الفندق',
        car_rental: 'تأجير السيارات',
        travel_insurance: 'تأمين السفر',
        itinerary_planning: 'تخطيط الرحلة',
        
        // Status
        pending: 'قيد المراجعة',
        in_progress: 'قيد التنفيذ',
        completed: 'مكتمل',
        rejected: 'مرفوض',
        cancelled: 'ملغي'
    },
    
    en: {
        // Navigation
        home: 'Home',
        services: 'Our Services',
        about: 'About Us',
        contact: 'Contact Us',
        login: 'Login',
        register: 'Register',
        dashboard: 'Dashboard',
        
        // Hero Section
        hero_title: 'Your Journey Starts Here with ENGAZ',
        hero_subtitle: 'Comprehensive platform for all travel services for Egyptians traveling abroad, from document processing to complete trip planning',
        start_journey: 'Start Your Journey Now',
        explore_services: 'Explore Our Services',
        
        // Services
        our_services: 'Our Services',
        services_subtitle: 'We provide a comprehensive range of services to make your travel easier and more comfortable',
        document_processing: 'Document Processing',
        document_processing_desc: 'We help you prepare and process all required travel documents',
        visa_assistance: 'Visa Assistance',
        visa_assistance_desc: 'Comprehensive guidance for the visa application process',
        travel_planning: 'Travel Planning',
        travel_planning_desc: 'Complete trip planning from flights to accommodation and activities',
        status_tracking: 'Status Tracking',
        status_tracking_desc: 'Track the status of your applications and services in real-time',
        learn_more: 'Learn More',
        
        // Features
        why_choose_us: 'Why Choose ENGAZ?',
        secure_reliable: 'Secure & Reliable',
        secure_reliable_desc: 'Complete protection for your personal data and sensitive information',
        fast_efficient: 'Fast & Efficient',
        fast_efficient_desc: 'Quick processing of your requests with continuous follow-up',
        expert_support: 'Expert Support',
        expert_support_desc: 'Team of experts available to help you 24/7',
        
        // Statistics
        happy_customers: 'Happy Customers',
        countries_served: 'Countries Served',
        success_rate: 'Success Rate %',
        support_hours: 'Support Hours',
        
        // CTA
        ready_to_travel: 'Ready to Travel?',
        start_your_journey: 'Start your journey with us today and get the best travel services',
        get_started: 'Get Started',
        
        // Footer
        footer_description: 'Comprehensive platform for travel services for Egyptians traveling abroad',
        quick_links: 'Quick Links',
        contact_info: 'Contact Information',
        follow_us: 'Follow Us',
        all_rights_reserved: 'All Rights Reserved',
        
        // Forms
        field_required: 'This field is required',
        invalid_email: 'Invalid email address',
        invalid_phone: 'Invalid phone number',
        password_min_length: 'Password must be at least 8 characters',
        form_success: 'Form submitted successfully',
        form_error: 'An error occurred while submitting the form',
        
        // Common
        loading: 'Loading...',
        submit: 'Submit',
        cancel: 'Cancel',
        close: 'Close',
        save: 'Save',
        edit: 'Edit',
        delete: 'Delete',
        confirm: 'Confirm',
        yes: 'Yes',
        no: 'No',
        
        // Login/Register
        email: 'Email',
        password: 'Password',
        confirm_password: 'Confirm Password',
        full_name: 'Full Name',
        phone: 'Phone Number',
        remember_me: 'Remember Me',
        forgot_password: 'Forgot Password?',
        create_account: 'Create New Account',
        already_have_account: 'Already have an account?',
        dont_have_account: "Don't have an account?",
        
        // Dashboard
        welcome_back: 'Welcome Back',
        my_applications: 'My Applications',
        my_documents: 'My Documents',
        profile_settings: 'Profile Settings',
        logout: 'Logout',
        
        // Document Processing
        upload_document: 'Upload Document',
        document_type: 'Document Type',
        passport: 'Passport',
        national_id: 'National ID',
        birth_certificate: 'Birth Certificate',
        marriage_certificate: 'Marriage Certificate',
        bank_statement: 'Bank Statement',
        employment_letter: 'Employment Letter',
        
        // Visa Assistance
        destination_country: 'Destination Country',
        visa_type: 'Visa Type',
        tourist_visa: 'Tourist Visa',
        business_visa: 'Business Visa',
        student_visa: 'Student Visa',
        family_visa: 'Family Visa',
        travel_date: 'Travel Date',
        return_date: 'Return Date',
        
        // Travel Planning
        flight_booking: 'Flight Booking',
        hotel_booking: 'Hotel Booking',
        car_rental: 'Car Rental',
        travel_insurance: 'Travel Insurance',
        itinerary_planning: 'Itinerary Planning',
        
        // Status
        pending: 'Pending',
        in_progress: 'In Progress',
        completed: 'Completed',
        rejected: 'Rejected',
        cancelled: 'Cancelled'
    }
};
