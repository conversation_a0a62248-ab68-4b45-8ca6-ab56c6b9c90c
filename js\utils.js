// ===== UTILITY FUNCTIONS =====

window.EngagUtils = {
    
    // ===== DATE UTILITIES =====
    
    // Format date to locale string
    formatDate(date, locale = 'ar-EG', options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        return new Date(date).toLocaleDateString(locale, { ...defaultOptions, ...options });
    },
    
    // Format date for input fields
    formatDateForInput(date) {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    },
    
    // Get relative time (e.g., "2 hours ago")
    getRelativeTime(date, locale = 'ar') {
        const now = new Date();
        const diffInSeconds = Math.floor((now - new Date(date)) / 1000);
        
        const intervals = {
            ar: {
                year: { single: 'سنة', plural: 'سنوات', value: 31536000 },
                month: { single: 'شهر', plural: 'أشهر', value: 2592000 },
                week: { single: 'أسبوع', plural: 'أسابيع', value: 604800 },
                day: { single: 'يوم', plural: 'أيام', value: 86400 },
                hour: { single: 'ساعة', plural: 'ساعات', value: 3600 },
                minute: { single: 'دقيقة', plural: 'دقائق', value: 60 },
                second: { single: 'ثانية', plural: 'ثوان', value: 1 }
            },
            en: {
                year: { single: 'year', plural: 'years', value: 31536000 },
                month: { single: 'month', plural: 'months', value: 2592000 },
                week: { single: 'week', plural: 'weeks', value: 604800 },
                day: { single: 'day', plural: 'days', value: 86400 },
                hour: { single: 'hour', plural: 'hours', value: 3600 },
                minute: { single: 'minute', plural: 'minutes', value: 60 },
                second: { single: 'second', plural: 'seconds', value: 1 }
            }
        };
        
        for (const [key, interval] of Object.entries(intervals[locale])) {
            const count = Math.floor(diffInSeconds / interval.value);
            if (count >= 1) {
                const unit = count === 1 ? interval.single : interval.plural;
                return locale === 'ar' ? `منذ ${count} ${unit}` : `${count} ${unit} ago`;
            }
        }
        
        return locale === 'ar' ? 'الآن' : 'now';
    },
    
    // ===== STRING UTILITIES =====
    
    // Capitalize first letter
    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    },
    
    // Convert to title case
    toTitleCase(str) {
        return str.replace(/\w\S*/g, (txt) => 
            txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
    },
    
    // Truncate string with ellipsis
    truncate(str, length = 100, suffix = '...') {
        if (str.length <= length) return str;
        return str.substring(0, length) + suffix;
    },
    
    // Remove HTML tags
    stripHtml(html) {
        const tmp = document.createElement('div');
        tmp.innerHTML = html;
        return tmp.textContent || tmp.innerText || '';
    },
    
    // Generate slug from string
    slugify(str) {
        return str
            .toLowerCase()
            .trim()
            .replace(/[^\w\s-]/g, '')
            .replace(/[\s_-]+/g, '-')
            .replace(/^-+|-+$/g, '');
    },
    
    // ===== NUMBER UTILITIES =====
    
    // Format number with locale
    formatNumber(num, locale = 'ar-EG', options = {}) {
        return new Intl.NumberFormat(locale, options).format(num);
    },
    
    // Format currency
    formatCurrency(amount, currency = 'EGP', locale = 'ar-EG') {
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency
        }).format(amount);
    },
    
    // Format percentage
    formatPercentage(value, decimals = 0) {
        return `${(value * 100).toFixed(decimals)}%`;
    },
    
    // Generate random number
    randomNumber(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    
    // ===== VALIDATION UTILITIES =====
    
    // Validate email
    isValidEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    },
    
    // Validate phone number
    isValidPhone(phone) {
        const regex = /^[\+]?[1-9][\d]{0,15}$/;
        return regex.test(phone.replace(/\s/g, ''));
    },
    
    // Validate Egyptian national ID
    isValidEgyptianID(id) {
        if (!/^\d{14}$/.test(id)) return false;
        
        // Check birth date
        const year = parseInt(id.substring(1, 3));
        const month = parseInt(id.substring(3, 5));
        const day = parseInt(id.substring(5, 7));
        
        const fullYear = year < 50 ? 2000 + year : 1900 + year;
        const birthDate = new Date(fullYear, month - 1, day);
        
        if (birthDate.getFullYear() !== fullYear || 
            birthDate.getMonth() !== month - 1 || 
            birthDate.getDate() !== day) {
            return false;
        }
        
        return true;
    },
    
    // Validate passport number
    isValidPassport(passport) {
        // Basic validation - alphanumeric, 6-9 characters
        const regex = /^[A-Z0-9]{6,9}$/;
        return regex.test(passport.toUpperCase());
    },
    
    // ===== STORAGE UTILITIES =====
    
    // Local storage with JSON support
    storage: {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (e) {
                console.error('Storage set error:', e);
                return false;
            }
        },
        
        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('Storage get error:', e);
                return defaultValue;
            }
        },
        
        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (e) {
                console.error('Storage remove error:', e);
                return false;
            }
        },
        
        clear() {
            try {
                localStorage.clear();
                return true;
            } catch (e) {
                console.error('Storage clear error:', e);
                return false;
            }
        }
    },
    
    // ===== URL UTILITIES =====
    
    // Get URL parameters
    getUrlParams() {
        const params = {};
        const urlSearchParams = new URLSearchParams(window.location.search);
        for (const [key, value] of urlSearchParams) {
            params[key] = value;
        }
        return params;
    },
    
    // Update URL parameter
    updateUrlParam(key, value) {
        const url = new URL(window.location);
        url.searchParams.set(key, value);
        window.history.replaceState({}, '', url);
    },
    
    // Remove URL parameter
    removeUrlParam(key) {
        const url = new URL(window.location);
        url.searchParams.delete(key);
        window.history.replaceState({}, '', url);
    },
    
    // ===== FILE UTILITIES =====
    
    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // Get file extension
    getFileExtension(filename) {
        return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
    },
    
    // Validate file type
    isValidFileType(file, allowedTypes) {
        const fileType = file.type;
        const fileExtension = this.getFileExtension(file.name).toLowerCase();
        
        return allowedTypes.some(type => {
            if (type.includes('/')) {
                return fileType === type;
            } else {
                return fileExtension === type.toLowerCase();
            }
        });
    },
    
    // ===== DEVICE UTILITIES =====
    
    // Detect device type
    getDeviceType() {
        const width = window.innerWidth;
        if (width < 640) return 'mobile';
        if (width < 1024) return 'tablet';
        return 'desktop';
    },
    
    // Check if mobile device
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },
    
    // Check if touch device
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },
    
    // ===== PERFORMANCE UTILITIES =====
    
    // Debounce function
    debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    },
    
    // Throttle function
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // ===== ANIMATION UTILITIES =====
    
    // Smooth scroll to element
    scrollToElement(element, offset = 0, duration = 500) {
        const targetPosition = element.offsetTop - offset;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        let startTime = null;
        
        function animation(currentTime) {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const run = this.easeInOutQuad(timeElapsed, startPosition, distance, duration);
            window.scrollTo(0, run);
            if (timeElapsed < duration) requestAnimationFrame(animation);
        }
        
        requestAnimationFrame(animation);
    },
    
    // Easing function
    easeInOutQuad(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    },
    
    // ===== MISC UTILITIES =====
    
    // Generate UUID
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },
    
    // Copy text to clipboard
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            return true;
        }
    },
    
    // Download file
    downloadFile(data, filename, type = 'text/plain') {
        const blob = new Blob([data], { type });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    },
    
    // Wait for element to exist
    waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }
            
            const observer = new MutationObserver(() => {
                const element = document.querySelector(selector);
                if (element) {
                    observer.disconnect();
                    resolve(element);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`Element ${selector} not found within ${timeout}ms`));
            }, timeout);
        });
    }
};
