/* ===== MOBILE FIRST RESPONSIVE DESIGN ===== */

/* Extra Small Devices (phones, 320px and up) */
@media (max-width: 479px) {
    .container {
        padding: 0 var(--space-3);
    }
    
    .hero-title {
        font-size: var(--text-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--text-base);
    }
    
    .hero-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .hero-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .section-title {
        font-size: var(--text-2xl);
    }
    
    .service-card {
        padding: var(--space-6);
    }
    
    .service-icon {
        width: 60px;
        height: 60px;
    }
    
    .service-icon i {
        font-size: var(--text-xl);
    }
    
    .stat-number {
        font-size: var(--text-3xl);
    }
    
    .cta-title {
        font-size: var(--text-2xl);
    }
    
    .footer-content {
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
}

/* Small Devices (landscape phones, 480px and up) */
@media (min-width: 480px) and (max-width: 639px) {
    .hero-actions {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Medium Devices (tablets, 640px and up) */
@media (min-width: 640px) and (max-width: 767px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .features-content {
        gap: var(--space-8);
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-6);
    }
}

/* Large Devices (small laptops, 768px and up) */
@media (min-width: 768px) and (max-width: 1023px) {
    .hero .container {
        gap: var(--space-8);
    }
    
    .hero-content {
        text-align: center;
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-6);
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .features-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .feature-item {
        flex-direction: column;
        text-align: center;
        align-items: center;
    }
}

/* Extra Large Devices (large laptops and desktops, 1024px and up) */
@media (min-width: 1024px) {
    .nav-menu {
        display: block;
    }
    
    .nav-toggle {
        display: none;
    }
    
    .hero .container {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-12);
    }
    
    .hero-content {
        text-align: right;
    }
    
    [dir="ltr"] .hero-content {
        text-align: left;
    }
    
    .hero-actions {
        justify-content: flex-start;
    }
    
    [dir="ltr"] .hero-actions {
        justify-content: flex-start;
    }
    
    .services-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .features-content {
        grid-template-columns: 1fr 1fr;
        text-align: right;
    }
    
    [dir="ltr"] .features-content {
        text-align: left;
    }
    
    .feature-item {
        flex-direction: row;
        text-align: right;
        align-items: flex-start;
    }
    
    [dir="ltr"] .feature-item {
        text-align: left;
    }
    
    .footer-content {
        grid-template-columns: 2fr 1fr 1fr 1fr;
    }
}

/* Extra Extra Large Devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1200px;
    }
    
    .hero-title {
        font-size: var(--text-6xl);
    }
    
    .hero-subtitle {
        font-size: var(--text-xl);
    }
    
    .section-title {
        font-size: var(--text-5xl);
    }
    
    .service-card {
        padding: var(--space-10);
    }
    
    .service-icon {
        width: 100px;
        height: 100px;
    }
    
    .service-icon i {
        font-size: var(--text-3xl);
    }
}

/* ===== MOBILE NAVIGATION ===== */
@media (max-width: 1023px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background: var(--white);
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
        z-index: var(--z-dropdown);
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-list {
        flex-direction: column;
        padding: var(--space-6);
        gap: var(--space-4);
    }
    
    .nav-link {
        padding: var(--space-3);
        border-bottom: 1px solid var(--gray-200);
        width: 100%;
        text-align: center;
    }
    
    .nav-link.active::after {
        display: none;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background: var(--gray-50);
        margin-top: var(--space-2);
        border-radius: var(--radius-md);
    }
    
    .nav-actions {
        flex-direction: column;
        gap: var(--space-2);
        width: 100%;
        padding: 0 var(--space-6) var(--space-6);
    }
    
    .nav-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .language-toggle {
        width: 100%;
        justify-content: center;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    .header,
    .footer,
    .nav-toggle,
    .back-to-top,
    .loading-screen {
        display: none !important;
    }
    
    .hero {
        min-height: auto;
        padding: var(--space-8) 0;
    }
    
    .hero-background,
    .hero-overlay {
        display: none;
    }
    
    .hero-content {
        color: black !important;
    }
    
    section {
        padding: var(--space-8) 0;
        page-break-inside: avoid;
    }
    
    .service-card,
    .feature-item,
    .stat-item {
        page-break-inside: avoid;
    }
    
    a {
        text-decoration: underline;
    }
    
    a[href^="http"]:after {
        content: " (" attr(href) ")";
    }
    
    .btn {
        border: 1px solid black;
        background: transparent !important;
        color: black !important;
    }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --primary-dark: #000080;
        --secondary-color: #ff8c00;
        --gray-600: #000000;
        --gray-700: #000000;
        --gray-800: #000000;
        --gray-900: #000000;
    }
    
    .btn-outline {
        border-width: 2px;
    }
    
    .service-card,
    .card {
        border: 2px solid var(--gray-300);
    }
    
    .nav-link:focus,
    .btn:focus,
    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
        outline: 3px solid var(--primary-color);
        outline-offset: 2px;
    }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .loading-screen .spinner {
        animation: none;
    }
    
    .hero-background {
        background: var(--primary-color);
    }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1f2937;
        --gray-50: #111827;
        --gray-100: #1f2937;
        --gray-200: #374151;
        --gray-300: #4b5563;
        --gray-400: #6b7280;
        --gray-500: #9ca3af;
        --gray-600: #d1d5db;
        --gray-700: #e5e7eb;
        --gray-800: #f3f4f6;
        --gray-900: #f9fafb;
    }
    
    body {
        background-color: var(--gray-50);
        color: var(--gray-800);
    }
    
    .header {
        background: rgba(31, 41, 55, 0.95);
        border-bottom-color: var(--gray-200);
    }
    
    .service-card,
    .card {
        background: var(--gray-100);
        border-color: var(--gray-200);
    }
    
    .footer {
        background: var(--gray-900);
    }
    
    .features {
        background: var(--gray-100);
    }
    
    .form-input,
    .form-select,
    .form-textarea {
        background: var(--gray-100);
        border-color: var(--gray-300);
        color: var(--gray-800);
    }
    
    .dropdown-menu {
        background: var(--gray-100);
        border: 1px solid var(--gray-200);
    }
    
    .modal-content {
        background: var(--gray-100);
    }
}

/* ===== LANDSCAPE ORIENTATION ===== */
@media (orientation: landscape) and (max-height: 600px) {
    .hero {
        min-height: auto;
        padding: var(--space-12) 0;
    }
    
    .hero-title {
        font-size: var(--text-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--text-base);
        margin-bottom: var(--space-6);
    }
    
    section {
        padding: var(--space-12) 0;
    }
}

/* ===== TOUCH DEVICE OPTIMIZATIONS ===== */
@media (hover: none) and (pointer: coarse) {
    .btn,
    .nav-link,
    .service-link,
    .social-links a {
        min-height: 44px;
        min-width: 44px;
    }
    
    .service-card:hover,
    .card:hover {
        transform: none;
    }
    
    .service-card:hover .service-icon {
        transform: none;
    }
    
    .dropdown:hover .dropdown-menu {
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
    }
    
    .nav-link:hover::after {
        width: 0;
    }
    
    .service-link:hover::after {
        width: 0;
    }
}

/* ===== FOCUS VISIBLE SUPPORT ===== */
@supports selector(:focus-visible) {
    .btn:focus,
    .nav-link:focus,
    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
        outline: none;
    }
    
    .btn:focus-visible,
    .nav-link:focus-visible,
    .form-input:focus-visible,
    .form-select:focus-visible,
    .form-textarea:focus-visible {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
}

/* ===== CONTAINER QUERIES (Future-proofing) ===== */
@supports (container-type: inline-size) {
    .services-overview {
        container-type: inline-size;
    }
    
    @container (max-width: 600px) {
        .services-grid {
            grid-template-columns: 1fr;
        }
    }
    
    @container (min-width: 601px) and (max-width: 900px) {
        .services-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @container (min-width: 901px) {
        .services-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }
}
