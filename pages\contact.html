<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="تواصل معنا - إنجز لخدمات السفر">
    <title>اتصل بنا - إنجز</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/icons/favicon.ico">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/animations.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="../index.html" class="logo">
                        <i class="fas fa-plane-departure"></i>
                        <span>إنجز</span>
                    </a>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="../index.html" class="nav-link" data-translate="home">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a href="services.html" class="nav-link" data-translate="services">خدماتنا</a>
                        </li>
                        <li class="nav-item">
                            <a href="about.html" class="nav-link" data-translate="about">من نحن</a>
                        </li>
                        <li class="nav-item">
                            <a href="contact.html" class="nav-link active" data-translate="contact">اتصل بنا</a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-actions">
                    <button class="language-toggle" id="language-toggle">
                        <i class="fas fa-globe"></i>
                        <span id="current-lang">EN</span>
                    </button>
                    <a href="login.html" class="btn btn-outline" data-translate="login">تسجيل الدخول</a>
                    <a href="register.html" class="btn btn-primary" data-translate="register">إنشاء حساب</a>
                    <button class="nav-toggle" id="nav-toggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <div class="page-header-content">
                    <h1 class="page-title" data-translate="contact_us">اتصل بنا</h1>
                    <p class="page-subtitle" data-translate="contact_subtitle">
                        نحن هنا لمساعدتك في جميع احتياجاتك للسفر. تواصل معنا في أي وقت
                    </p>
                    <nav class="breadcrumb">
                        <a href="../index.html" data-translate="home">الرئيسية</a>
                        <span>/</span>
                        <span data-translate="contact">اتصل بنا</span>
                    </nav>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact-section">
            <div class="container">
                <div class="contact-content">
                    <!-- Contact Form -->
                    <div class="contact-form-container">
                        <div class="section-header">
                            <h2 class="section-title" data-translate="send_message">أرسل لنا رسالة</h2>
                            <p class="section-subtitle" data-translate="form_subtitle">
                                املأ النموذج أدناه وسنتواصل معك في أقرب وقت ممكن
                            </p>
                        </div>
                        
                        <form id="contact-form" class="contact-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="first_name" class="form-label" data-translate="first_name">الاسم الأول</label>
                                    <input 
                                        type="text" 
                                        id="first_name" 
                                        name="first_name" 
                                        class="form-input" 
                                        placeholder="أدخل اسمك الأول"
                                        required
                                    >
                                </div>
                                
                                <div class="form-group">
                                    <label for="last_name" class="form-label" data-translate="last_name">الاسم الأخير</label>
                                    <input 
                                        type="text" 
                                        id="last_name" 
                                        name="last_name" 
                                        class="form-input" 
                                        placeholder="أدخل اسمك الأخير"
                                        required
                                    >
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="email" class="form-label" data-translate="email">البريد الإلكتروني</label>
                                    <input 
                                        type="email" 
                                        id="email" 
                                        name="email" 
                                        class="form-input" 
                                        placeholder="أدخل بريدك الإلكتروني"
                                        required
                                    >
                                </div>
                                
                                <div class="form-group">
                                    <label for="phone" class="form-label" data-translate="phone">رقم الهاتف</label>
                                    <input 
                                        type="tel" 
                                        id="phone" 
                                        name="phone" 
                                        class="form-input" 
                                        placeholder="+20 ************"
                                    >
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="subject" class="form-label" data-translate="subject">الموضوع</label>
                                <select id="subject" name="subject" class="form-select" required>
                                    <option value="">اختر الموضوع</option>
                                    <option value="general" data-translate="general_inquiry">استفسار عام</option>
                                    <option value="services" data-translate="services_inquiry">استفسار عن الخدمات</option>
                                    <option value="support" data-translate="technical_support">دعم فني</option>
                                    <option value="complaint" data-translate="complaint">شكوى</option>
                                    <option value="suggestion" data-translate="suggestion">اقتراح</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="message" class="form-label" data-translate="message">الرسالة</label>
                                <textarea 
                                    id="message" 
                                    name="message" 
                                    class="form-textarea" 
                                    placeholder="اكتب رسالتك هنا..."
                                    rows="6"
                                    required
                                ></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-large" data-translate="send_message">
                                إرسال الرسالة
                            </button>
                        </form>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="contact-info-container">
                        <div class="section-header">
                            <h2 class="section-title" data-translate="contact_info">معلومات الاتصال</h2>
                            <p class="section-subtitle" data-translate="contact_info_subtitle">
                                يمكنك التواصل معنا من خلال الطرق التالية
                            </p>
                        </div>
                        
                        <div class="contact-info-list">
                            <div class="contact-info-item">
                                <div class="contact-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="contact-details">
                                    <h3 data-translate="address">العنوان</h3>
                                    <p>123 شارع التحرير، وسط البلد<br>القاهرة، مصر</p>
                                </div>
                            </div>
                            
                            <div class="contact-info-item">
                                <div class="contact-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div class="contact-details">
                                    <h3 data-translate="phone">الهاتف</h3>
                                    <p>+20 ************<br>+20 ************</p>
                                </div>
                            </div>
                            
                            <div class="contact-info-item">
                                <div class="contact-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="contact-details">
                                    <h3 data-translate="email">البريد الإلكتروني</h3>
                                    <p><EMAIL><br><EMAIL></p>
                                </div>
                            </div>
                            
                            <div class="contact-info-item">
                                <div class="contact-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="contact-details">
                                    <h3 data-translate="working_hours">ساعات العمل</h3>
                                    <p>الأحد - الخميس: 9:00 ص - 6:00 م<br>الجمعة - السبت: 10:00 ص - 4:00 م</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Social Links -->
                        <div class="contact-social">
                            <h3 data-translate="follow_us">تابعنا على</h3>
                            <div class="social-links">
                                <a href="#" aria-label="Facebook" class="social-link">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" aria-label="Twitter" class="social-link">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" aria-label="Instagram" class="social-link">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <a href="#" aria-label="LinkedIn" class="social-link">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <a href="#" aria-label="WhatsApp" class="social-link">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="faq-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title" data-translate="faq">الأسئلة الشائعة</h2>
                    <p class="section-subtitle" data-translate="faq_subtitle">
                        إجابات على الأسئلة الأكثر شيوعاً حول خدماتنا
                    </p>
                </div>
                
                <div class="faq-list">
                    <div class="accordion-item">
                        <div class="accordion-header">
                            <h3 class="accordion-title">كم يستغرق معالجة طلب التأشيرة؟</h3>
                            <i class="fas fa-chevron-down accordion-icon"></i>
                        </div>
                        <div class="accordion-content">
                            <div class="accordion-body">
                                <p>يختلف وقت معالجة طلب التأشيرة حسب نوع التأشيرة والدولة المقصودة. عادة ما يستغرق الأمر من 5 إلى 15 يوم عمل. سنقوم بإعلامك بالوقت المتوقع عند تقديم طلبك.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <div class="accordion-header">
                            <h3 class="accordion-title">ما هي الوثائق المطلوبة للسفر؟</h3>
                            <i class="fas fa-chevron-down accordion-icon"></i>
                        </div>
                        <div class="accordion-content">
                            <div class="accordion-body">
                                <p>تختلف الوثائق المطلوبة حسب وجهة السفر وغرض الرحلة. بشكل عام، ستحتاج إلى جواز سفر ساري المفعول، تأشيرة (إذا كانت مطلوبة)، تذكرة طيران، وحجز فندق. سنقوم بتزويدك بقائمة مفصلة حسب وجهتك.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <div class="accordion-header">
                            <h3 class="accordion-title">هل يمكنني تتبع حالة طلبي؟</h3>
                            <i class="fas fa-chevron-down accordion-icon"></i>
                        </div>
                        <div class="accordion-content">
                            <div class="accordion-body">
                                <p>نعم، يمكنك تتبع حالة طلبك في الوقت الفعلي من خلال لوحة التحكم الخاصة بك. ستتلقى أيضاً إشعارات عبر البريد الإلكتروني والرسائل النصية عند تحديث حالة طلبك.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <div class="accordion-header">
                            <h3 class="accordion-title">ما هي طرق الدفع المتاحة؟</h3>
                            <i class="fas fa-chevron-down accordion-icon"></i>
                        </div>
                        <div class="accordion-content">
                            <div class="accordion-body">
                                <p>نقبل جميع طرق الدفع الرئيسية بما في ذلك البطاقات الائتمانية (فيزا، ماستركارد)، التحويل البنكي، والدفع النقدي في مكاتبنا. كما نوفر خيارات دفع آمنة عبر الإنترنت.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="../index.html" class="logo">
                            <i class="fas fa-plane-departure"></i>
                            <span>إنجز</span>
                        </a>
                        <p data-translate="footer_description">
                            منصة شاملة لخدمات السفر للمصريين المسافرين للخارج
                        </p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h3 data-translate="quick_links">روابط سريعة</h3>
                    <ul class="footer-links">
                        <li><a href="services.html" data-translate="services">خدماتنا</a></li>
                        <li><a href="about.html" data-translate="about">من نحن</a></li>
                        <li><a href="contact.html" data-translate="contact">اتصل بنا</a></li>
                        <li><a href="dashboard.html" data-translate="dashboard">لوحة التحكم</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3 data-translate="contact_info">معلومات الاتصال</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> +20 ************</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> القاهرة، مصر</p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h3 data-translate="follow_us">تابعنا</h3>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 إنجز. <span data-translate="all_rights_reserved">جميع الحقوق محفوظة</span></p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="back-to-top" aria-label="العودة للأعلى">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- JavaScript Files -->
    <script src="../js/utils.js"></script>
    <script src="../js/language.js"></script>
    <script src="../js/main.js"></script>
    
    <script>
        // Initialize accordion functionality
        document.querySelectorAll('.accordion-item').forEach(item => {
            const header = item.querySelector('.accordion-header');
            header.addEventListener('click', () => {
                const isActive = item.classList.contains('active');
                
                // Close all accordion items
                document.querySelectorAll('.accordion-item').forEach(otherItem => {
                    otherItem.classList.remove('active');
                });
                
                // Toggle current item
                if (!isActive) {
                    item.classList.add('active');
                }
            });
        });
    </script>
    
    <style>
        /* Contact-specific styles */
        .contact-section {
            padding: var(--space-20) 0;
            background: var(--white);
        }
        
        .contact-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--space-12);
        }
        
        @media (min-width: 1024px) {
            .contact-content {
                grid-template-columns: 2fr 1fr;
            }
        }
        
        .contact-form-container,
        .contact-info-container {
            background: var(--white);
        }
        
        @media (min-width: 1024px) {
            .contact-info-container {
                background: var(--gray-50);
                padding: var(--space-8);
                border-radius: var(--radius-2xl);
            }
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--space-4);
        }
        
        @media (min-width: 640px) {
            .form-row {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        .contact-info-list {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }
        
        .contact-info-item {
            display: flex;
            gap: var(--space-4);
        }
        
        .contact-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .contact-icon i {
            font-size: var(--text-lg);
            color: var(--white);
        }
        
        .contact-details h3 {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-2);
            color: var(--gray-900);
        }
        
        .contact-details p {
            color: var(--gray-600);
            margin: 0;
            line-height: 1.6;
        }
        
        .contact-social h3 {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-4);
            color: var(--gray-900);
        }
        
        .contact-social .social-links {
            display: flex;
            gap: var(--space-3);
        }
        
        .social-link {
            width: 45px;
            height: 45px;
            background: var(--gray-200);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-600);
            text-decoration: none;
            transition: all var(--transition-fast);
        }
        
        .social-link:hover {
            background: var(--primary-color);
            color: var(--white);
            transform: translateY(-2px);
        }
        
        .faq-section {
            background: var(--gray-50);
            padding: var(--space-20) 0;
        }
        
        .faq-list {
            max-width: 800px;
            margin: 0 auto;
        }
    </style>
</body>
</html>
