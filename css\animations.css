/* ===== CSS ANIMATIONS ===== */

/* ===== KEYFRAME ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translateY(0);
    }
    40%, 43% {
        transform: translateY(-15px);
    }
    70% {
        transform: translateY(-7px);
    }
    90% {
        transform: translateY(-3px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--primary-color);
    }
    50% {
        box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
    }
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

/* ===== ANIMATION UTILITY CLASSES ===== */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

.animate-slide-in-down {
    animation: slideInDown 0.5s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.4s ease-out;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-rotate {
    animation: rotate 1s linear infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

.animate-gradient {
    background: linear-gradient(-45deg, var(--primary-color), var(--secondary-color), var(--accent-color), var(--primary-light));
    background-size: 400% 400%;
    animation: gradient 3s ease infinite;
}

/* ===== HOVER ANIMATIONS ===== */
.hover-lift {
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.hover-scale {
    transition: transform var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform var(--transition-normal);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

.hover-slide {
    position: relative;
    overflow: hidden;
}

.hover-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.hover-slide:hover::before {
    left: 100%;
}

/* ===== LOADING ANIMATIONS ===== */
.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: loading-dots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-dots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-bars {
    display: inline-flex;
    gap: 4px;
}

.loading-bars span {
    width: 4px;
    height: 20px;
    background: var(--primary-color);
    animation: loading-bars 1.2s ease-in-out infinite;
}

.loading-bars span:nth-child(1) { animation-delay: -1.1s; }
.loading-bars span:nth-child(2) { animation-delay: -1.0s; }
.loading-bars span:nth-child(3) { animation-delay: -0.9s; }

@keyframes loading-bars {
    0%, 40%, 100% {
        transform: scaleY(0.4);
    }
    20% {
        transform: scaleY(1);
    }
}

/* ===== SCROLL ANIMATIONS ===== */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease-out;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

.scroll-reveal-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease-out;
}

.scroll-reveal-left.revealed {
    opacity: 1;
    transform: translateX(0);
}

.scroll-reveal-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease-out;
}

.scroll-reveal-right.revealed {
    opacity: 1;
    transform: translateX(0);
}

.scroll-reveal-scale {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease-out;
}

.scroll-reveal-scale.revealed {
    opacity: 1;
    transform: scale(1);
}

/* ===== STAGGER ANIMATIONS ===== */
.stagger-children > * {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.stagger-children.animate > *:nth-child(1) { transition-delay: 0.1s; }
.stagger-children.animate > *:nth-child(2) { transition-delay: 0.2s; }
.stagger-children.animate > *:nth-child(3) { transition-delay: 0.3s; }
.stagger-children.animate > *:nth-child(4) { transition-delay: 0.4s; }
.stagger-children.animate > *:nth-child(5) { transition-delay: 0.5s; }
.stagger-children.animate > *:nth-child(6) { transition-delay: 0.6s; }

.stagger-children.animate > * {
    opacity: 1;
    transform: translateY(0);
}

/* ===== PROGRESS ANIMATIONS ===== */
.progress-animated .progress-bar {
    animation: progress-fill 2s ease-out;
}

@keyframes progress-fill {
    from {
        width: 0;
    }
}

/* ===== COUNTER ANIMATIONS ===== */
.counter {
    transition: all 0.3s ease;
}

.counter.counting {
    color: var(--primary-color);
    transform: scale(1.1);
}

/* ===== TYPEWRITER EFFECT ===== */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typewriter 3s steps(40, end), blink 0.75s step-end infinite;
}

/* ===== PARALLAX EFFECT ===== */
.parallax {
    transform: translateZ(0);
    transition: transform 0.1s ease-out;
}

/* ===== MORPHING SHAPES ===== */
.morph-circle {
    border-radius: 50%;
    transition: border-radius var(--transition-slow);
}

.morph-circle:hover {
    border-radius: 20%;
}

/* ===== FLIP ANIMATIONS ===== */
.flip-card {
    perspective: 1000px;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
}

.flip-card-back {
    transform: rotateY(180deg);
}

/* ===== SLIDE ANIMATIONS ===== */
.slide-in-left {
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
}

.slide-in-left.active {
    transform: translateX(0);
}

.slide-in-right {
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.slide-in-right.active {
    transform: translateX(0);
}

/* ===== ZOOM ANIMATIONS ===== */
.zoom-in {
    transform: scale(0);
    transition: transform var(--transition-normal);
}

.zoom-in.active {
    transform: scale(1);
}

.zoom-out {
    transform: scale(1.2);
    transition: transform var(--transition-normal);
}

.zoom-out.active {
    transform: scale(1);
}

/* ===== ELASTIC ANIMATIONS ===== */
@keyframes elastic {
    0% {
        transform: scale(1);
    }
    30% {
        transform: scale(1.25);
    }
    40% {
        transform: scale(0.75);
    }
    50% {
        transform: scale(1.15);
    }
    65% {
        transform: scale(0.95);
    }
    75% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.animate-elastic {
    animation: elastic 0.6s ease-out;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.will-change-auto {
    will-change: auto;
}

/* Force hardware acceleration */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* ===== REDUCED MOTION OVERRIDES ===== */
@media (prefers-reduced-motion: reduce) {
    .animate-fade-in,
    .animate-fade-in-up,
    .animate-fade-in-down,
    .animate-fade-in-left,
    .animate-fade-in-right,
    .animate-slide-in-up,
    .animate-slide-in-down,
    .animate-scale-in,
    .animate-bounce,
    .animate-pulse,
    .animate-shake,
    .animate-rotate,
    .animate-float,
    .animate-glow,
    .animate-gradient,
    .animate-elastic {
        animation: none;
    }
    
    .hover-lift:hover,
    .hover-scale:hover,
    .hover-rotate:hover {
        transform: none;
    }
    
    .scroll-reveal,
    .scroll-reveal-left,
    .scroll-reveal-right,
    .scroll-reveal-scale {
        opacity: 1;
        transform: none;
        transition: none;
    }
    
    .stagger-children > * {
        opacity: 1;
        transform: none;
        transition: none;
    }
    
    .typewriter {
        animation: none;
        border-right: none;
        white-space: normal;
        overflow: visible;
    }
    
    .flip-card:hover .flip-card-inner {
        transform: none;
    }
    
    .loading-dots span,
    .loading-bars span {
        animation: none;
    }
    
    .loading-spinner {
        animation: none;
        border-top-color: var(--gray-400);
    }
}
