<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="إنشاء حساب جديد في منصة إنجز لخدمات السفر">
    <title>إنشاء حساب - إنجز</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/icons/favicon.ico">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/animations.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="../index.html" class="logo">
                        <i class="fas fa-plane-departure"></i>
                        <span>إنجز</span>
                    </a>
                </div>
                
                <div class="nav-actions">
                    <button class="language-toggle" id="language-toggle">
                        <i class="fas fa-globe"></i>
                        <span id="current-lang">EN</span>
                    </button>
                    <a href="../index.html" class="btn btn-outline" data-translate="back_to_home">العودة للرئيسية</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <div class="auth-logo">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h1 class="auth-title" data-translate="register">إنشاء حساب جديد</h1>
                    <p class="auth-subtitle" data-translate="register_subtitle">انضم إلينا واستمتع بأفضل خدمات السفر</p>
                </div>
                
                <form id="register-form" class="auth-form">
                    <div class="form-group">
                        <label for="full_name" class="form-label" data-translate="full_name">الاسم الكامل</label>
                        <div class="form-input-group">
                            <i class="fas fa-user"></i>
                            <input 
                                type="text" 
                                id="full_name" 
                                name="full_name" 
                                class="form-input" 
                                placeholder="أدخل اسمك الكامل"
                                data-translate-placeholder="full_name_placeholder"
                                required
                            >
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label" data-translate="email">البريد الإلكتروني</label>
                        <div class="form-input-group">
                            <i class="fas fa-envelope"></i>
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                class="form-input" 
                                placeholder="أدخل بريدك الإلكتروني"
                                data-translate-placeholder="email_placeholder"
                                required
                            >
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone" class="form-label" data-translate="phone">رقم الهاتف</label>
                        <div class="form-input-group">
                            <i class="fas fa-phone"></i>
                            <input 
                                type="tel" 
                                id="phone" 
                                name="phone" 
                                class="form-input" 
                                placeholder="+20 ************"
                                data-translate-placeholder="phone_placeholder"
                                required
                            >
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label" data-translate="password">كلمة المرور</label>
                        <div class="form-input-group">
                            <i class="fas fa-lock"></i>
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="form-input" 
                                placeholder="أدخل كلمة المرور"
                                data-translate-placeholder="password_placeholder"
                                required
                                minlength="8"
                            >
                            <button type="button" class="password-toggle" data-target="password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength">
                            <div class="password-strength-bar">
                                <div class="password-strength-fill"></div>
                            </div>
                            <span class="password-strength-text">قوة كلمة المرور</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password" class="form-label" data-translate="confirm_password">تأكيد كلمة المرور</label>
                        <div class="form-input-group">
                            <i class="fas fa-lock"></i>
                            <input 
                                type="password" 
                                id="confirm_password" 
                                name="confirm_password" 
                                class="form-input" 
                                placeholder="أعد إدخال كلمة المرور"
                                data-translate-placeholder="confirm_password_placeholder"
                                required
                            >
                            <button type="button" class="password-toggle" data-target="confirm_password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="terms" required>
                            <span class="checkmark"></span>
                            <span>
                                أوافق على 
                                <a href="terms.html" class="auth-link" data-translate="terms_conditions">الشروط والأحكام</a>
                                و
                                <a href="privacy.html" class="auth-link" data-translate="privacy_policy">سياسة الخصوصية</a>
                            </span>
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="newsletter">
                            <span class="checkmark"></span>
                            <span data-translate="subscribe_newsletter">أريد الحصول على النشرة الإخبارية والعروض الخاصة</span>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-large btn-full" data-original-text="إنشاء حساب" data-translate="create_account">
                        إنشاء حساب
                    </button>
                </form>
                
                <div class="auth-divider">
                    <span data-translate="or">أو</span>
                </div>
                
                <div class="social-login">
                    <button class="btn btn-outline btn-social">
                        <i class="fab fa-google"></i>
                        <span data-translate="register_with_google">إنشاء حساب بجوجل</span>
                    </button>
                    
                    <button class="btn btn-outline btn-social">
                        <i class="fab fa-facebook-f"></i>
                        <span data-translate="register_with_facebook">إنشاء حساب بفيسبوك</span>
                    </button>
                </div>
                
                <div class="auth-footer">
                    <p>
                        <span data-translate="already_have_account">لديك حساب بالفعل؟</span>
                        <a href="login.html" class="auth-link" data-translate="login">تسجيل الدخول</a>
                    </p>
                </div>
            </div>
            
            <div class="auth-image">
                <img src="../assets/images/register-illustration.svg" alt="Register Illustration" loading="lazy">
                <div class="auth-image-content">
                    <h2 data-translate="join_us">انضم إلينا</h2>
                    <p data-translate="register_image_text">ابدأ رحلتك معنا واكتشف عالماً من الخدمات المميزة</p>
                    
                    <div class="features-list">
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span data-translate="feature_1">خدمات سفر شاملة</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span data-translate="feature_2">دعم على مدار الساعة</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span data-translate="feature_3">أسعار تنافسية</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="auth-footer-simple">
        <div class="container">
            <p>&copy; 2024 إنجز. <span data-translate="all_rights_reserved">جميع الحقوق محفوظة</span></p>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="../js/utils.js"></script>
    <script src="../js/language.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/main.js"></script>
    
    <script>
        // Password toggle functionality
        document.querySelectorAll('.password-toggle').forEach(toggle => {
            toggle.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const passwordField = document.getElementById(targetId);
                const icon = this.querySelector('i');
                
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
        
        // Password strength indicator
        const passwordField = document.getElementById('password');
        const strengthBar = document.querySelector('.password-strength-fill');
        const strengthText = document.querySelector('.password-strength-text');
        
        passwordField.addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);
            
            strengthBar.style.width = strength.percentage + '%';
            strengthBar.className = 'password-strength-fill ' + strength.class;
            strengthText.textContent = strength.text;
        });
        
        function calculatePasswordStrength(password) {
            let score = 0;
            let feedback = [];
            
            if (password.length >= 8) score += 25;
            if (/[a-z]/.test(password)) score += 25;
            if (/[A-Z]/.test(password)) score += 25;
            if (/[0-9]/.test(password)) score += 25;
            if (/[^A-Za-z0-9]/.test(password)) score += 25;
            
            if (score <= 25) {
                return { percentage: 25, class: 'weak', text: 'ضعيفة' };
            } else if (score <= 50) {
                return { percentage: 50, class: 'fair', text: 'متوسطة' };
            } else if (score <= 75) {
                return { percentage: 75, class: 'good', text: 'جيدة' };
            } else {
                return { percentage: 100, class: 'strong', text: 'قوية' };
            }
        }
        
        // Social login handlers
        document.querySelectorAll('.btn-social').forEach(button => {
            button.addEventListener('click', function() {
                const provider = this.querySelector('i').classList.contains('fa-google') ? 'google' : 'facebook';
                // In real app, this would initiate OAuth flow
                alert(`إنشاء حساب بـ ${provider === 'google' ? 'جوجل' : 'فيسبوك'} غير متاح حالياً`);
            });
        });
    </script>
    
    <style>
        /* Additional styles for register page */
        .password-strength {
            margin-top: var(--space-2);
        }
        
        .password-strength-bar {
            width: 100%;
            height: 4px;
            background: var(--gray-200);
            border-radius: var(--radius-full);
            overflow: hidden;
        }
        
        .password-strength-fill {
            height: 100%;
            transition: all var(--transition-normal);
            border-radius: var(--radius-full);
        }
        
        .password-strength-fill.weak {
            background: var(--error-color);
        }
        
        .password-strength-fill.fair {
            background: var(--warning-color);
        }
        
        .password-strength-fill.good {
            background: var(--info-color);
        }
        
        .password-strength-fill.strong {
            background: var(--success-color);
        }
        
        .password-strength-text {
            font-size: var(--text-xs);
            color: var(--gray-500);
            margin-top: var(--space-1);
            display: block;
        }
        
        .features-list {
            margin-top: var(--space-6);
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }
        
        .features-list .feature-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            color: var(--gray-700);
        }
        
        .features-list .feature-item i {
            color: var(--success-color);
            font-size: var(--text-sm);
        }
        
        .features-list .feature-item span {
            font-size: var(--text-sm);
        }
        
        /* Responsive adjustments */
        @media (max-width: 640px) {
            .auth-card {
                padding: var(--space-6);
            }
            
            .auth-title {
                font-size: var(--text-2xl);
            }
        }
    </style>
</body>
</html>
