/* ===== SERVICES GRID ===== */
.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);
}

@media (min-width: 640px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .services-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.service-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-100);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    transition: all var(--transition-normal);
}

.service-card:hover .service-icon {
    transform: scale(1.1);
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
}

.service-icon i {
    font-size: var(--text-2xl);
    color: var(--white);
}

.service-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    margin-bottom: var(--space-4);
    color: var(--gray-900);
}

.service-description {
    color: var(--gray-600);
    margin-bottom: var(--space-6);
    line-height: 1.6;
}

.service-link {
    color: var(--primary-color);
    font-weight: var(--font-medium);
    text-decoration: none;
    position: relative;
    transition: color var(--transition-fast);
}

.service-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width var(--transition-fast);
}

.service-link:hover::after {
    width: 100%;
}

/* ===== FEATURES SECTION ===== */
.features {
    background: var(--gray-50);
}

.features-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-12);
    align-items: center;
}

@media (min-width: 1024px) {
    .features-content {
        grid-template-columns: 1fr 1fr;
    }
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-8);
}

.feature-item {
    display: flex;
    gap: var(--space-4);
    align-items: flex-start;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: var(--text-xl);
    color: var(--white);
}

.feature-content h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    margin-bottom: var(--space-2);
    color: var(--gray-900);
}

.feature-content p {
    color: var(--gray-600);
    margin: 0;
}

.features-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.features-image img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.1));
}

/* ===== STATISTICS SECTION ===== */
.statistics {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-8);
}

@media (min-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--space-2);
    color: var(--secondary-color);
}

@media (min-width: 768px) {
    .stat-number {
        font-size: var(--text-5xl);
    }
}

.stat-label {
    font-size: var(--text-lg);
    opacity: 0.9;
}

/* ===== CTA SECTION ===== */
.cta {
    background: linear-gradient(135deg, var(--gray-900), var(--gray-800));
    color: var(--white);
    text-align: center;
}

.cta-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--space-4);
}

@media (min-width: 768px) {
    .cta-title {
        font-size: var(--text-4xl);
    }
}

.cta-subtitle {
    font-size: var(--text-lg);
    margin-bottom: var(--space-8);
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--gray-900);
    color: var(--white);
    padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);
    margin-bottom: var(--space-12);
}

@media (min-width: 640px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .footer-content {
        grid-template-columns: 2fr 1fr 1fr 1fr;
    }
}

.footer-section h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    margin-bottom: var(--space-4);
    color: var(--white);
}

.footer-brand .logo {
    margin-bottom: var(--space-4);
}

.footer-brand p {
    color: var(--gray-400);
    line-height: 1.6;
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.footer-links a {
    color: var(--gray-400);
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--white);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    color: var(--gray-400);
    margin: 0;
}

.contact-info i {
    color: var(--primary-color);
    width: 20px;
}

.social-links {
    display: flex;
    gap: var(--space-4);
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    transition: all var(--transition-fast);
}

.social-links a:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: var(--space-8);
    text-align: center;
    color: var(--gray-400);
}

/* ===== FORMS ===== */
.form-group {
    margin-bottom: var(--space-6);
}

.form-label {
    display: block;
    font-weight: var(--font-medium);
    margin-bottom: var(--space-2);
    color: var(--gray-700);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    transition: all var(--transition-fast);
    background: var(--white);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-error {
    color: var(--error-color);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
}

.form-success {
    color: var(--success-color);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
}

/* ===== CARDS ===== */
.card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

/* ===== BADGES ===== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background: var(--primary-color);
    color: var(--white);
}

.badge-success {
    background: var(--success-color);
    color: var(--white);
}

.badge-warning {
    background: var(--warning-color);
    color: var(--white);
}

.badge-error {
    background: var(--error-color);
    color: var(--white);
}

.badge-outline {
    background: transparent;
    border: 1px solid currentColor;
}

/* ===== PROGRESS BAR ===== */
.progress {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
}

/* ===== ALERTS ===== */
.alert {
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* ===== MODAL ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--text-xl);
    cursor: pointer;
    color: var(--gray-500);
    transition: color var(--transition-fast);
}

.modal-close:hover {
    color: var(--gray-700);
}

.modal-body {
    padding: var(--space-6);
}

.modal-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
}

/* ===== TABS ===== */
.tabs {
    border-bottom: 1px solid var(--gray-200);
}

.tab-list {
    display: flex;
    list-style: none;
    gap: var(--space-1);
}

.tab-button {
    padding: var(--space-3) var(--space-6);
    background: none;
    border: none;
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    color: var(--gray-600);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
}

.tab-button:hover {
    color: var(--primary-color);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    padding: var(--space-6) 0;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* ===== DROPDOWN ===== */
.dropdown-toggle {
    background: var(--white);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.dropdown-toggle:hover {
    border-color: var(--primary-color);
}

.dropdown-toggle i {
    transition: transform var(--transition-fast);
}

.dropdown.active .dropdown-toggle i {
    transform: rotate(180deg);
}

/* ===== ACCORDION ===== */
.accordion-item {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-2);
    overflow: hidden;
}

.accordion-header {
    background: var(--gray-50);
    padding: var(--space-4);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background var(--transition-fast);
}

.accordion-header:hover {
    background: var(--gray-100);
}

.accordion-title {
    font-weight: var(--font-medium);
    margin: 0;
}

.accordion-icon {
    transition: transform var(--transition-fast);
}

.accordion-item.active .accordion-icon {
    transform: rotate(180deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-normal);
}

.accordion-item.active .accordion-content {
    max-height: 500px;
}

.accordion-body {
    padding: var(--space-4);
}

/* ===== PAGE HEADER ===== */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: var(--space-20) 0 var(--space-16);
    margin-top: 80px;
    text-align: center;
}

.page-header-content {
    max-width: 800px;
    margin: 0 auto;
}

.page-title {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--space-4);
}

@media (min-width: 768px) {
    .page-title {
        font-size: var(--text-5xl);
    }
}

.page-subtitle {
    font-size: var(--text-lg);
    opacity: 0.9;
    margin-bottom: var(--space-6);
    line-height: 1.6;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    opacity: 0.8;
}

.breadcrumb a {
    color: var(--white);
    text-decoration: none;
    transition: opacity var(--transition-fast);
}

.breadcrumb a:hover {
    opacity: 0.8;
}

/* ===== SERVICES FILTER ===== */
.services-filter {
    background: var(--white);
    padding: var(--space-8) 0;
    border-bottom: 1px solid var(--gray-200);
}

.filter-tabs {
    display: flex;
    justify-content: center;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.filter-tab {
    background: var(--gray-100);
    border: none;
    border-radius: var(--radius-full);
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-tab:hover {
    background: var(--gray-200);
    color: var(--gray-700);
}

.filter-tab.active {
    background: var(--primary-color);
    color: var(--white);
}

/* ===== DETAILED SERVICE CARDS ===== */
.service-card.detailed {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-100);
    display: flex;
    flex-direction: column;
}

.service-card.detailed:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.service-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.service-card.detailed:hover .service-image img {
    transform: scale(1.05);
}

.service-badge {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

[dir="rtl"] .service-badge {
    right: auto;
    left: var(--space-4);
}

.service-badge i {
    font-size: var(--text-lg);
    color: var(--primary-color);
}

.service-content {
    padding: var(--space-6);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.service-card.detailed .service-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    margin-bottom: var(--space-3);
    color: var(--gray-900);
}

.service-card.detailed .service-description {
    color: var(--gray-600);
    margin-bottom: var(--space-4);
    line-height: 1.6;
    flex: 1;
}

.service-features {
    list-style: none;
    margin-bottom: var(--space-6);
}

.service-features li {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-2);
    font-size: var(--text-sm);
    color: var(--gray-700);
}

.service-features i {
    color: var(--success-color);
    font-size: var(--text-xs);
}

.service-price {
    display: flex;
    align-items: baseline;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
}

.price-from {
    font-size: var(--text-sm);
    color: var(--gray-500);
}

.price-amount {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    color: var(--primary-color);
}

/* ===== PROCESS SECTION ===== */
.process-section {
    background: var(--gray-50);
    padding: var(--space-20) 0;
}

.process-steps {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);
    max-width: 800px;
    margin: 0 auto;
}

@media (min-width: 768px) {
    .process-steps {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .process-steps {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-6);
    }
}

.process-step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    margin: 0 auto var(--space-4);
    position: relative;
    z-index: 1;
}

.step-content h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    margin-bottom: var(--space-2);
    color: var(--gray-900);
}

.step-content p {
    color: var(--gray-600);
    font-size: var(--text-sm);
    line-height: 1.5;
    margin: 0;
}

/* Process step connectors */
@media (min-width: 1024px) {
    .process-step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 30px;
        left: calc(100% - 30px);
        width: calc(100% - 60px);
        height: 2px;
        background: var(--gray-300);
        z-index: 0;
    }

    [dir="rtl"] .process-step:not(:last-child)::after {
        left: auto;
        right: calc(100% - 30px);
    }
}

/* ===== DASHBOARD STYLES ===== */
.dashboard-main {
    padding-top: 100px;
    padding-bottom: var(--space-20);
    min-height: 100vh;
    background: var(--gray-50);
}

.dashboard-header {
    text-align: center;
    margin-bottom: var(--space-12);
}

.dashboard-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--space-2);
    color: var(--gray-900);
}

@media (min-width: 768px) {
    .dashboard-title {
        font-size: var(--text-4xl);
    }
}

.dashboard-subtitle {
    color: var(--gray-600);
    font-size: var(--text-lg);
    margin: 0;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-12);
}

@media (min-width: 640px) {
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .dashboard-stats {
        grid-template-columns: repeat(4, 1fr);
    }
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-100);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon i {
    font-size: var(--text-xl);
    color: var(--white);
}

.stat-number {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--space-1);
    color: var(--gray-900);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin: 0;
}

/* Dashboard Content */
.dashboard-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);
}

@media (min-width: 1024px) {
    .dashboard-content {
        grid-template-columns: 2fr 1fr;
    }
}

.dashboard-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-100);
}

.dashboard-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
}

.dashboard-section .section-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    margin: 0;
    color: var(--gray-900);
}

.section-link {
    color: var(--primary-color);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    text-decoration: none;
    background: none;
    border: none;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.section-link:hover {
    color: var(--primary-dark);
}

/* Applications List */
.applications-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.application-item {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.application-item:hover {
    border-color: var(--primary-color);
    background: var(--gray-50);
}

.application-icon {
    width: 50px;
    height: 50px;
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.application-icon i {
    font-size: var(--text-lg);
    color: var(--primary-color);
}

.application-info {
    flex: 1;
}

.application-title {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-1);
    color: var(--gray-900);
}

.application-details {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin: 0;
}

.application-status {
    margin: 0 var(--space-4);
}

.status-badge {
    display: inline-block;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-in-progress {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.status-completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-rejected {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-4);
}

@media (min-width: 640px) {
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

.quick-action-card {
    display: block;
    padding: var(--space-4);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: all var(--transition-fast);
    text-align: center;
}

.quick-action-card:hover {
    border-color: var(--primary-color);
    background: var(--gray-50);
    transform: translateY(-2px);
}

.action-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-3);
}

.action-icon i {
    font-size: var(--text-lg);
    color: var(--white);
}

.action-title {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-2);
    color: var(--gray-900);
}

.action-description {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin: 0;
}

/* Notifications */
.notifications-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.notification-item {
    display: flex;
    gap: var(--space-3);
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    transition: background var(--transition-fast);
}

.notification-item.unread {
    background: var(--gray-50);
}

.notification-item:hover {
    background: var(--gray-100);
}

.notification-icon {
    width: 40px;
    height: 40px;
    background: var(--gray-200);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-icon i {
    font-size: var(--text-base);
    color: var(--primary-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-1);
    color: var(--gray-900);
}

.notification-message {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-bottom: var(--space-1);
    line-height: 1.4;
}

.notification-time {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

/* User Menu */
.user-menu {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.user-menu:hover {
    background: var(--gray-100);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    display: none;
    flex-direction: column;
}

@media (min-width: 768px) {
    .user-info {
        display: flex;
    }
}

.user-name {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--gray-900);
}

.user-email {
    font-size: var(--text-xs);
    color: var(--gray-600);
}

.user-menu-toggle {
    background: none;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.user-menu.active .user-menu-toggle {
    transform: rotate(180deg);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--space-2);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: var(--z-dropdown);
}

[dir="rtl"] .user-dropdown {
    right: auto;
    left: 0;
}

.user-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2) var(--space-3);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    transition: all var(--transition-fast);
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--primary-color);
}

.dropdown-divider {
    margin: var(--space-2) 0;
    border: none;
    border-top: 1px solid var(--gray-200);
}
