// ===== AUTHENTICATION SYSTEM =====

window.EngagAuth = {
    
    // Configuration
    config: {
        tokenKey: 'engaz-auth-token',
        userKey: 'engaz-user-data',
        sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
        apiEndpoint: '/api/auth'
    },
    
    // Current user state
    currentUser: null,
    isAuthenticated: false,
    
    // Initialize authentication system
    init() {
        this.checkAuthStatus();
        this.bindEvents();
    },
    
    // Check if user is authenticated
    checkAuthStatus() {
        const token = this.getToken();
        const userData = this.getUserData();
        
        if (token && userData && this.isTokenValid(token)) {
            this.currentUser = userData;
            this.isAuthenticated = true;
            this.updateUI();
        } else {
            this.logout();
        }
    },
    
    // Bind authentication events
    bindEvents() {
        // Login form
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }
        
        // Register form
        const registerForm = document.getElementById('register-form');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }
        
        // Logout buttons
        const logoutButtons = document.querySelectorAll('[data-action="logout"]');
        logoutButtons.forEach(button => {
            button.addEventListener('click', (e) => this.handleLogout(e));
        });
        
        // Forgot password form
        const forgotPasswordForm = document.getElementById('forgot-password-form');
        if (forgotPasswordForm) {
            forgotPasswordForm.addEventListener('submit', (e) => this.handleForgotPassword(e));
        }
    },
    
    // Handle login form submission
    async handleLogin(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);
        
        const email = formData.get('email');
        const password = formData.get('password');
        const rememberMe = formData.get('remember_me');
        
        // Validate form
        if (!this.validateLoginForm(email, password)) {
            return;
        }
        
        // Show loading state
        const submitButton = form.querySelector('button[type="submit"]');
        this.setButtonLoading(submitButton, true);
        
        try {
            const result = await this.login(email, password, rememberMe);
            
            if (result.success) {
                // Redirect to dashboard or intended page
                const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || 'pages/dashboard.html';
                window.location.href = redirectUrl;
            } else {
                this.showError(result.message || 'فشل في تسجيل الدخول');
            }
        } catch (error) {
            this.showError('حدث خطأ أثناء تسجيل الدخول');
        } finally {
            this.setButtonLoading(submitButton, false);
        }
    },
    
    // Handle register form submission
    async handleRegister(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);
        
        const userData = {
            fullName: formData.get('full_name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            password: formData.get('password'),
            confirmPassword: formData.get('confirm_password')
        };
        
        // Validate form
        if (!this.validateRegisterForm(userData)) {
            return;
        }
        
        // Show loading state
        const submitButton = form.querySelector('button[type="submit"]');
        this.setButtonLoading(submitButton, true);
        
        try {
            const result = await this.register(userData);
            
            if (result.success) {
                this.showSuccess('تم إنشاء الحساب بنجاح! يرجى تسجيل الدخول');
                // Redirect to login page
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            } else {
                this.showError(result.message || 'فشل في إنشاء الحساب');
            }
        } catch (error) {
            this.showError('حدث خطأ أثناء إنشاء الحساب');
        } finally {
            this.setButtonLoading(submitButton, false);
        }
    },
    
    // Handle logout
    handleLogout(e) {
        e.preventDefault();
        this.logout();
        window.location.href = 'index.html';
    },
    
    // Handle forgot password
    async handleForgotPassword(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);
        const email = formData.get('email');
        
        if (!EngagUtils.isValidEmail(email)) {
            this.showError('يرجى إدخال بريد إلكتروني صحيح');
            return;
        }
        
        const submitButton = form.querySelector('button[type="submit"]');
        this.setButtonLoading(submitButton, true);
        
        try {
            const result = await this.forgotPassword(email);
            
            if (result.success) {
                this.showSuccess('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني');
            } else {
                this.showError(result.message || 'فشل في إرسال رابط إعادة التعيين');
            }
        } catch (error) {
            this.showError('حدث خطأ أثناء إرسال رابط إعادة التعيين');
        } finally {
            this.setButtonLoading(submitButton, false);
        }
    },
    
    // Login API call (simulated)
    async login(email, password, rememberMe) {
        // Simulate API call
        await this.delay(1500);
        
        // Mock authentication - in real app, this would be an API call
        const mockUsers = [
            {
                id: 1,
                email: '<EMAIL>',
                password: 'password123',
                fullName: 'أحمد محمد',
                phone: '+201234567890',
                avatar: 'assets/images/avatar-placeholder.jpg'
            }
        ];
        
        const user = mockUsers.find(u => u.email === email && u.password === password);
        
        if (user) {
            const token = this.generateToken();
            const userData = { ...user };
            delete userData.password;
            
            // Store authentication data
            this.setToken(token);
            this.setUserData(userData);
            
            this.currentUser = userData;
            this.isAuthenticated = true;
            this.updateUI();
            
            return { success: true, user: userData, token };
        } else {
            return { success: false, message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة' };
        }
    },
    
    // Register API call (simulated)
    async register(userData) {
        // Simulate API call
        await this.delay(2000);
        
        // Mock registration - in real app, this would be an API call
        // Check if email already exists
        const existingUsers = EngagUtils.storage.get('engaz-users', []);
        const emailExists = existingUsers.some(user => user.email === userData.email);
        
        if (emailExists) {
            return { success: false, message: 'البريد الإلكتروني مستخدم بالفعل' };
        }
        
        // Create new user
        const newUser = {
            id: Date.now(),
            fullName: userData.fullName,
            email: userData.email,
            phone: userData.phone,
            avatar: 'assets/images/avatar-placeholder.jpg',
            createdAt: new Date().toISOString()
        };
        
        // Save user (in real app, this would be done on server)
        existingUsers.push(newUser);
        EngagUtils.storage.set('engaz-users', existingUsers);
        
        return { success: true, user: newUser };
    },
    
    // Forgot password API call (simulated)
    async forgotPassword(email) {
        // Simulate API call
        await this.delay(1500);
        
        // Mock forgot password - in real app, this would send an email
        return { success: true };
    },
    
    // Logout
    logout() {
        this.removeToken();
        this.removeUserData();
        this.currentUser = null;
        this.isAuthenticated = false;
        this.updateUI();
    },
    
    // Validate login form
    validateLoginForm(email, password) {
        let isValid = true;
        
        if (!email) {
            this.showFieldError('email', 'البريد الإلكتروني مطلوب');
            isValid = false;
        } else if (!EngagUtils.isValidEmail(email)) {
            this.showFieldError('email', 'البريد الإلكتروني غير صحيح');
            isValid = false;
        }
        
        if (!password) {
            this.showFieldError('password', 'كلمة المرور مطلوبة');
            isValid = false;
        }
        
        return isValid;
    },
    
    // Validate register form
    validateRegisterForm(userData) {
        let isValid = true;
        
        if (!userData.fullName) {
            this.showFieldError('full_name', 'الاسم الكامل مطلوب');
            isValid = false;
        }
        
        if (!userData.email) {
            this.showFieldError('email', 'البريد الإلكتروني مطلوب');
            isValid = false;
        } else if (!EngagUtils.isValidEmail(userData.email)) {
            this.showFieldError('email', 'البريد الإلكتروني غير صحيح');
            isValid = false;
        }
        
        if (!userData.phone) {
            this.showFieldError('phone', 'رقم الهاتف مطلوب');
            isValid = false;
        } else if (!EngagUtils.isValidPhone(userData.phone)) {
            this.showFieldError('phone', 'رقم الهاتف غير صحيح');
            isValid = false;
        }
        
        if (!userData.password) {
            this.showFieldError('password', 'كلمة المرور مطلوبة');
            isValid = false;
        } else if (userData.password.length < 8) {
            this.showFieldError('password', 'كلمة المرور يجب أن تكون 8 أحرف على الأقل');
            isValid = false;
        }
        
        if (userData.password !== userData.confirmPassword) {
            this.showFieldError('confirm_password', 'كلمة المرور غير متطابقة');
            isValid = false;
        }
        
        return isValid;
    },
    
    // Token management
    getToken() {
        return EngagUtils.storage.get(this.config.tokenKey);
    },
    
    setToken(token) {
        EngagUtils.storage.set(this.config.tokenKey, token);
    },
    
    removeToken() {
        EngagUtils.storage.remove(this.config.tokenKey);
    },
    
    isTokenValid(token) {
        // In real app, this would validate with server
        // For now, just check if token exists and is not expired
        if (!token || !token.expiresAt) return false;
        return new Date(token.expiresAt) > new Date();
    },
    
    generateToken() {
        return {
            value: EngagUtils.generateUUID(),
            expiresAt: new Date(Date.now() + this.config.sessionTimeout).toISOString()
        };
    },
    
    // User data management
    getUserData() {
        return EngagUtils.storage.get(this.config.userKey);
    },
    
    setUserData(userData) {
        EngagUtils.storage.set(this.config.userKey, userData);
    },
    
    removeUserData() {
        EngagUtils.storage.remove(this.config.userKey);
    },
    
    // UI updates
    updateUI() {
        // Update navigation
        const loginButton = document.querySelector('a[href*="login"]');
        const registerButton = document.querySelector('a[href*="register"]');
        const userMenu = document.querySelector('.user-menu');
        
        if (this.isAuthenticated) {
            if (loginButton) loginButton.style.display = 'none';
            if (registerButton) registerButton.style.display = 'none';
            if (userMenu) userMenu.style.display = 'block';
        } else {
            if (loginButton) loginButton.style.display = 'block';
            if (registerButton) registerButton.style.display = 'block';
            if (userMenu) userMenu.style.display = 'none';
        }
        
        // Update user info displays
        const userNameElements = document.querySelectorAll('[data-user="name"]');
        const userEmailElements = document.querySelectorAll('[data-user="email"]');
        const userAvatarElements = document.querySelectorAll('[data-user="avatar"]');
        
        if (this.currentUser) {
            userNameElements.forEach(el => el.textContent = this.currentUser.fullName);
            userEmailElements.forEach(el => el.textContent = this.currentUser.email);
            userAvatarElements.forEach(el => el.src = this.currentUser.avatar);
        }
    },
    
    // Utility functions
    showError(message) {
        this.showAlert('error', message);
    },
    
    showSuccess(message) {
        this.showAlert('success', message);
    },
    
    showAlert(type, message) {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            <span>${message}</span>
        `;
        
        // Insert at top of page
        document.body.insertBefore(alert, document.body.firstChild);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            alert.remove();
        }, 5000);
    },
    
    showFieldError(fieldName, message) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.classList.add('error');
            
            let errorElement = field.parentNode.querySelector('.form-error');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'form-error';
                field.parentNode.appendChild(errorElement);
            }
            
            errorElement.textContent = message;
        }
    },
    
    setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
        } else {
            button.disabled = false;
            button.innerHTML = button.getAttribute('data-original-text') || 'إرسال';
        }
    },
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },
    
    // Check if user has permission
    hasPermission(permission) {
        if (!this.isAuthenticated || !this.currentUser) return false;
        
        // In real app, this would check user roles/permissions
        return true;
    },
    
    // Require authentication for page
    requireAuth() {
        if (!this.isAuthenticated) {
            const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
            window.location.href = `login.html?redirect=${currentUrl}`;
            return false;
        }
        return true;
    }
};

// Initialize authentication system when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => EngagAuth.init());
} else {
    EngagAuth.init();
}
