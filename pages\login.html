<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="تسجيل الدخول إلى منصة إنجز لخدمات السفر">
    <title>تسجيل الدخول - إنجز</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/icons/favicon.ico">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/animations.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="../index.html" class="logo">
                        <i class="fas fa-plane-departure"></i>
                        <span>إنجز</span>
                    </a>
                </div>
                
                <div class="nav-actions">
                    <button class="language-toggle" id="language-toggle">
                        <i class="fas fa-globe"></i>
                        <span id="current-lang">EN</span>
                    </button>
                    <a href="../index.html" class="btn btn-outline" data-translate="back_to_home">العودة للرئيسية</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <div class="auth-logo">
                        <i class="fas fa-plane-departure"></i>
                    </div>
                    <h1 class="auth-title" data-translate="login">تسجيل الدخول</h1>
                    <p class="auth-subtitle" data-translate="login_subtitle">مرحباً بعودتك! يرجى تسجيل الدخول إلى حسابك</p>
                </div>
                
                <form id="login-form" class="auth-form">
                    <div class="form-group">
                        <label for="email" class="form-label" data-translate="email">البريد الإلكتروني</label>
                        <div class="form-input-group">
                            <i class="fas fa-envelope"></i>
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                class="form-input" 
                                placeholder="أدخل بريدك الإلكتروني"
                                data-translate-placeholder="email_placeholder"
                                required
                            >
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label" data-translate="password">كلمة المرور</label>
                        <div class="form-input-group">
                            <i class="fas fa-lock"></i>
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="form-input" 
                                placeholder="أدخل كلمة المرور"
                                data-translate-placeholder="password_placeholder"
                                required
                            >
                            <button type="button" class="password-toggle" data-target="password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group form-options">
                        <label class="checkbox-label">
                            <input type="checkbox" name="remember_me" value="1">
                            <span class="checkmark"></span>
                            <span data-translate="remember_me">تذكرني</span>
                        </label>
                        
                        <a href="forgot-password.html" class="forgot-password-link" data-translate="forgot_password">نسيت كلمة المرور؟</a>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-large btn-full" data-original-text="تسجيل الدخول" data-translate="login">
                        تسجيل الدخول
                    </button>
                </form>
                
                <div class="auth-divider">
                    <span data-translate="or">أو</span>
                </div>
                
                <div class="social-login">
                    <button class="btn btn-outline btn-social">
                        <i class="fab fa-google"></i>
                        <span data-translate="login_with_google">تسجيل الدخول بجوجل</span>
                    </button>
                    
                    <button class="btn btn-outline btn-social">
                        <i class="fab fa-facebook-f"></i>
                        <span data-translate="login_with_facebook">تسجيل الدخول بفيسبوك</span>
                    </button>
                </div>
                
                <div class="auth-footer">
                    <p>
                        <span data-translate="dont_have_account">ليس لديك حساب؟</span>
                        <a href="register.html" class="auth-link" data-translate="create_account">إنشاء حساب جديد</a>
                    </p>
                </div>
            </div>
            
            <div class="auth-image">
                <img src="../assets/images/login-illustration.svg" alt="Login Illustration" loading="lazy">
                <div class="auth-image-content">
                    <h2 data-translate="welcome_back">مرحباً بعودتك</h2>
                    <p data-translate="login_image_text">ابدأ رحلتك معنا واستمتع بأفضل خدمات السفر</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="auth-footer-simple">
        <div class="container">
            <p>&copy; 2024 إنجز. <span data-translate="all_rights_reserved">جميع الحقوق محفوظة</span></p>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="../js/utils.js"></script>
    <script src="../js/language.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/main.js"></script>
    
    <script>
        // Password toggle functionality
        document.querySelectorAll('.password-toggle').forEach(toggle => {
            toggle.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const passwordField = document.getElementById(targetId);
                const icon = this.querySelector('i');
                
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
        
        // Social login handlers
        document.querySelectorAll('.btn-social').forEach(button => {
            button.addEventListener('click', function() {
                const provider = this.querySelector('i').classList.contains('fa-google') ? 'google' : 'facebook';
                // In real app, this would initiate OAuth flow
                alert(`تسجيل الدخول بـ ${provider === 'google' ? 'جوجل' : 'فيسبوك'} غير متاح حالياً`);
            });
        });
    </script>
    
    <style>
        /* Auth-specific styles */
        .auth-main {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: var(--space-20) 0;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
        }
        
        .auth-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--space-12);
            align-items: center;
        }
        
        @media (min-width: 1024px) {
            .auth-container {
                grid-template-columns: 1fr 1fr;
                padding: 0 var(--space-8);
            }
        }
        
        .auth-card {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-10);
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--gray-100);
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: var(--space-8);
        }
        
        .auth-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-6);
        }
        
        .auth-logo i {
            font-size: var(--text-2xl);
            color: var(--white);
        }
        
        .auth-title {
            font-size: var(--text-3xl);
            font-weight: var(--font-bold);
            margin-bottom: var(--space-2);
            color: var(--gray-900);
        }
        
        .auth-subtitle {
            color: var(--gray-600);
            margin: 0;
        }
        
        .auth-form {
            margin-bottom: var(--space-8);
        }
        
        .form-input-group {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .form-input-group i {
            position: absolute;
            left: var(--space-4);
            color: var(--gray-400);
            z-index: 1;
        }
        
        [dir="rtl"] .form-input-group i {
            left: auto;
            right: var(--space-4);
        }
        
        .form-input-group .form-input {
            padding-left: var(--space-12);
            padding-right: var(--space-4);
        }
        
        [dir="rtl"] .form-input-group .form-input {
            padding-left: var(--space-4);
            padding-right: var(--space-12);
        }
        
        .password-toggle {
            position: absolute;
            right: var(--space-4);
            background: none;
            border: none;
            color: var(--gray-400);
            cursor: pointer;
            padding: var(--space-2);
            z-index: 1;
        }
        
        [dir="rtl"] .password-toggle {
            right: auto;
            left: var(--space-4);
        }
        
        .password-toggle:hover {
            color: var(--gray-600);
        }
        
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            cursor: pointer;
            font-size: var(--text-sm);
        }
        
        .checkbox-label input[type="checkbox"] {
            display: none;
        }
        
        .checkmark {
            width: 20px;
            height: 20px;
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-sm);
            position: relative;
            transition: all var(--transition-fast);
        }
        
        .checkbox-label input[type="checkbox"]:checked + .checkmark {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 6px;
            width: 6px;
            height: 10px;
            border: solid var(--white);
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        
        .forgot-password-link {
            font-size: var(--text-sm);
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .forgot-password-link:hover {
            text-decoration: underline;
        }
        
        .btn-full {
            width: 100%;
        }
        
        .auth-divider {
            text-align: center;
            margin: var(--space-6) 0;
            position: relative;
        }
        
        .auth-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gray-200);
        }
        
        .auth-divider span {
            background: var(--white);
            padding: 0 var(--space-4);
            color: var(--gray-500);
            font-size: var(--text-sm);
        }
        
        .social-login {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
            margin-bottom: var(--space-8);
        }
        
        .btn-social {
            justify-content: center;
            gap: var(--space-3);
        }
        
        .auth-footer p {
            text-align: center;
            color: var(--gray-600);
            font-size: var(--text-sm);
            margin: 0;
        }
        
        .auth-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: var(--font-medium);
        }
        
        .auth-link:hover {
            text-decoration: underline;
        }
        
        .auth-image {
            display: none;
            text-align: center;
        }
        
        @media (min-width: 1024px) {
            .auth-image {
                display: block;
            }
        }
        
        .auth-image img {
            max-width: 100%;
            height: auto;
            margin-bottom: var(--space-6);
        }
        
        .auth-image-content h2 {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            margin-bottom: var(--space-4);
            color: var(--gray-900);
        }
        
        .auth-image-content p {
            color: var(--gray-600);
            font-size: var(--text-lg);
            margin: 0;
        }
        
        .auth-footer-simple {
            background: var(--gray-50);
            padding: var(--space-4) 0;
            text-align: center;
            color: var(--gray-600);
            font-size: var(--text-sm);
        }
    </style>
</body>
</html>
