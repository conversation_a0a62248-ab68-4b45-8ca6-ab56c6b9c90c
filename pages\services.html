<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="خدمات السفر الشاملة من إنجز - معالجة الوثائق، مساعدة التأشيرة، تخطيط السفر">
    <title>خدماتنا - إنجز</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/icons/favicon.ico">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/animations.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="../index.html" class="logo">
                        <i class="fas fa-plane-departure"></i>
                        <span>إنجز</span>
                    </a>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="../index.html" class="nav-link" data-translate="home">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a href="services.html" class="nav-link active" data-translate="services">خدماتنا</a>
                        </li>
                        <li class="nav-item">
                            <a href="about.html" class="nav-link" data-translate="about">من نحن</a>
                        </li>
                        <li class="nav-item">
                            <a href="contact.html" class="nav-link" data-translate="contact">اتصل بنا</a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-actions">
                    <button class="language-toggle" id="language-toggle">
                        <i class="fas fa-globe"></i>
                        <span id="current-lang">EN</span>
                    </button>
                    <a href="login.html" class="btn btn-outline" data-translate="login">تسجيل الدخول</a>
                    <a href="register.html" class="btn btn-primary" data-translate="register">إنشاء حساب</a>
                    <button class="nav-toggle" id="nav-toggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <div class="page-header-content">
                    <h1 class="page-title" data-translate="our_services">خدماتنا</h1>
                    <p class="page-subtitle" data-translate="services_page_subtitle">
                        نقدم مجموعة شاملة من الخدمات المتخصصة لجعل رحلتك أسهل وأكثر راحة
                    </p>
                    <nav class="breadcrumb">
                        <a href="../index.html" data-translate="home">الرئيسية</a>
                        <span>/</span>
                        <span data-translate="services">خدماتنا</span>
                    </nav>
                </div>
            </div>
        </section>

        <!-- Services Filter -->
        <section class="services-filter">
            <div class="container">
                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all" data-translate="all_services">جميع الخدمات</button>
                    <button class="filter-tab" data-filter="documents" data-translate="document_services">خدمات الوثائق</button>
                    <button class="filter-tab" data-filter="visa" data-translate="visa_services">خدمات التأشيرة</button>
                    <button class="filter-tab" data-filter="travel" data-translate="travel_services">خدمات السفر</button>
                    <button class="filter-tab" data-filter="consultation" data-translate="consultation_services">خدمات الاستشارة</button>
                </div>
            </div>
        </section>

        <!-- Services Grid -->
        <section class="services-section">
            <div class="container">
                <div class="services-grid" id="services-grid">
                    <!-- Document Processing Services -->
                    <div class="service-card detailed" data-category="documents" data-aos="fade-up" data-aos-delay="100">
                        <div class="service-image">
                            <img src="../assets/images/document-processing.jpg" alt="Document Processing" loading="lazy">
                            <div class="service-badge">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3 class="service-title" data-translate="document_processing">معالجة الوثائق</h3>
                            <p class="service-description" data-translate="document_processing_detailed">
                                نساعدك في إعداد وتجهيز جميع الوثائق المطلوبة للسفر بما في ذلك ترجمة المستندات، 
                                التصديقات، والتوثيق من الجهات المختصة
                            </p>
                            <ul class="service-features">
                                <li><i class="fas fa-check"></i> ترجمة معتمدة للوثائق</li>
                                <li><i class="fas fa-check"></i> تصديق من وزارة الخارجية</li>
                                <li><i class="fas fa-check"></i> توثيق من السفارات</li>
                                <li><i class="fas fa-check"></i> مراجعة شاملة للمستندات</li>
                            </ul>
                            <div class="service-price">
                                <span class="price-from">ابتداءً من</span>
                                <span class="price-amount">500 جنيه</span>
                            </div>
                            <a href="document-processing.html" class="btn btn-primary" data-translate="learn_more">اعرف المزيد</a>
                        </div>
                    </div>

                    <!-- Visa Assistance -->
                    <div class="service-card detailed" data-category="visa" data-aos="fade-up" data-aos-delay="200">
                        <div class="service-image">
                            <img src="../assets/images/visa-assistance.jpg" alt="Visa Assistance" loading="lazy">
                            <div class="service-badge">
                                <i class="fas fa-passport"></i>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3 class="service-title" data-translate="visa_assistance">مساعدة التأشيرة</h3>
                            <p class="service-description" data-translate="visa_assistance_detailed">
                                إرشاد شامل لعملية التقديم للحصول على التأشيرة مع متابعة مستمرة حتى الحصول على الموافقة
                            </p>
                            <ul class="service-features">
                                <li><i class="fas fa-check"></i> استشارة مجانية للتأشيرة</li>
                                <li><i class="fas fa-check"></i> ملء النماذج المطلوبة</li>
                                <li><i class="fas fa-check"></i> حجز المواعيد</li>
                                <li><i class="fas fa-check"></i> متابعة حالة الطلب</li>
                            </ul>
                            <div class="service-price">
                                <span class="price-from">ابتداءً من</span>
                                <span class="price-amount">1000 جنيه</span>
                            </div>
                            <a href="visa-assistance.html" class="btn btn-primary" data-translate="learn_more">اعرف المزيد</a>
                        </div>
                    </div>

                    <!-- Travel Planning -->
                    <div class="service-card detailed" data-category="travel" data-aos="fade-up" data-aos-delay="300">
                        <div class="service-image">
                            <img src="../assets/images/travel-planning.jpg" alt="Travel Planning" loading="lazy">
                            <div class="service-badge">
                                <i class="fas fa-map-marked-alt"></i>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3 class="service-title" data-translate="travel_planning">تخطيط السفر</h3>
                            <p class="service-description" data-translate="travel_planning_detailed">
                                تخطيط شامل لرحلتك من حجز الطيران والفنادق إلى تنظيم الأنشطة والجولات السياحية
                            </p>
                            <ul class="service-features">
                                <li><i class="fas fa-check"></i> حجز تذاكر الطيران</li>
                                <li><i class="fas fa-check"></i> حجز الفنادق</li>
                                <li><i class="fas fa-check"></i> تأجير السيارات</li>
                                <li><i class="fas fa-check"></i> تأمين السفر</li>
                            </ul>
                            <div class="service-price">
                                <span class="price-from">ابتداءً من</span>
                                <span class="price-amount">300 جنيه</span>
                            </div>
                            <a href="travel-planning.html" class="btn btn-primary" data-translate="learn_more">اعرف المزيد</a>
                        </div>
                    </div>

                    <!-- Flight Booking -->
                    <div class="service-card detailed" data-category="travel" data-aos="fade-up" data-aos-delay="400">
                        <div class="service-image">
                            <img src="../assets/images/flight-booking.jpg" alt="Flight Booking" loading="lazy">
                            <div class="service-badge">
                                <i class="fas fa-plane"></i>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3 class="service-title" data-translate="flight_booking">حجز الطيران</h3>
                            <p class="service-description" data-translate="flight_booking_detailed">
                                احصل على أفضل أسعار تذاكر الطيران مع خيارات متنوعة تناسب ميزانيتك وجدولك الزمني
                            </p>
                            <ul class="service-features">
                                <li><i class="fas fa-check"></i> أفضل الأسعار المضمونة</li>
                                <li><i class="fas fa-check"></i> خيارات دفع متنوعة</li>
                                <li><i class="fas fa-check"></i> إمكانية التعديل والإلغاء</li>
                                <li><i class="fas fa-check"></i> دعم على مدار الساعة</li>
                            </ul>
                            <div class="service-price">
                                <span class="price-from">رسوم الخدمة</span>
                                <span class="price-amount">100 جنيه</span>
                            </div>
                            <a href="flight-booking.html" class="btn btn-primary" data-translate="learn_more">اعرف المزيد</a>
                        </div>
                    </div>

                    <!-- Hotel Booking -->
                    <div class="service-card detailed" data-category="travel" data-aos="fade-up" data-aos-delay="500">
                        <div class="service-image">
                            <img src="../assets/images/hotel-booking.jpg" alt="Hotel Booking" loading="lazy">
                            <div class="service-badge">
                                <i class="fas fa-bed"></i>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3 class="service-title" data-translate="hotel_booking">حجز الفنادق</h3>
                            <p class="service-description" data-translate="hotel_booking_detailed">
                                اختر من بين آلاف الفنادق حول العالم مع ضمان أفضل الأسعار وخدمة عملاء متميزة
                            </p>
                            <ul class="service-features">
                                <li><i class="fas fa-check"></i> آلاف الفنادق المتاحة</li>
                                <li><i class="fas fa-check"></i> تقييمات حقيقية من النزلاء</li>
                                <li><i class="fas fa-check"></i> إلغاء مجاني للعديد من الفنادق</li>
                                <li><i class="fas fa-check"></i> عروض وخصومات حصرية</li>
                            </ul>
                            <div class="service-price">
                                <span class="price-from">رسوم الخدمة</span>
                                <span class="price-amount">50 جنيه</span>
                            </div>
                            <a href="hotel-booking.html" class="btn btn-primary" data-translate="learn_more">اعرف المزيد</a>
                        </div>
                    </div>

                    <!-- Travel Consultation -->
                    <div class="service-card detailed" data-category="consultation" data-aos="fade-up" data-aos-delay="600">
                        <div class="service-image">
                            <img src="../assets/images/consultation.jpg" alt="Travel Consultation" loading="lazy">
                            <div class="service-badge">
                                <i class="fas fa-user-tie"></i>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3 class="service-title" data-translate="travel_consultation">استشارة السفر</h3>
                            <p class="service-description" data-translate="consultation_detailed">
                                احصل على استشارة مجانية من خبراء السفر لدينا لتخطيط رحلتك بأفضل طريقة ممكنة
                            </p>
                            <ul class="service-features">
                                <li><i class="fas fa-check"></i> استشارة مجانية لمدة 30 دقيقة</li>
                                <li><i class="fas fa-check"></i> نصائح من خبراء السفر</li>
                                <li><i class="fas fa-check"></i> توصيات مخصصة</li>
                                <li><i class="fas fa-check"></i> متابعة مستمرة</li>
                            </ul>
                            <div class="service-price">
                                <span class="price-from">الاستشارة الأولى</span>
                                <span class="price-amount">مجانية</span>
                            </div>
                            <a href="consultation.html" class="btn btn-primary" data-translate="learn_more">اعرف المزيد</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Process Section -->
        <section class="process-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title" data-translate="how_it_works">كيف نعمل</h2>
                    <p class="section-subtitle" data-translate="process_subtitle">
                        عملية بسيطة وواضحة للحصول على خدماتنا
                    </p>
                </div>
                
                <div class="process-steps">
                    <div class="process-step" data-aos="fade-up" data-aos-delay="100">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3 data-translate="step_1_title">اختر الخدمة</h3>
                            <p data-translate="step_1_desc">اختر الخدمة التي تحتاجها من قائمة خدماتنا الشاملة</p>
                        </div>
                    </div>
                    
                    <div class="process-step" data-aos="fade-up" data-aos-delay="200">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3 data-translate="step_2_title">قدم طلبك</h3>
                            <p data-translate="step_2_desc">املأ النموذج المطلوب وأرفق الوثائق اللازمة</p>
                        </div>
                    </div>
                    
                    <div class="process-step" data-aos="fade-up" data-aos-delay="300">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3 data-translate="step_3_title">تابع التقدم</h3>
                            <p data-translate="step_3_desc">تابع حالة طلبك من خلال لوحة التحكم الخاصة بك</p>
                        </div>
                    </div>
                    
                    <div class="process-step" data-aos="fade-up" data-aos-delay="400">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h3 data-translate="step_4_title">احصل على النتيجة</h3>
                            <p data-translate="step_4_desc">استلم خدمتك مكتملة في الوقت المحدد</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="cta">
            <div class="container">
                <div class="cta-content">
                    <h2 class="cta-title" data-translate="ready_to_start">مستعد للبدء؟</h2>
                    <p class="cta-subtitle" data-translate="start_your_service">ابدأ خدمتك معنا اليوم واحصل على أفضل تجربة سفر</p>
                    <a href="register.html" class="btn btn-primary btn-large" data-translate="get_started">ابدأ الآن</a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="../index.html" class="logo">
                            <i class="fas fa-plane-departure"></i>
                            <span>إنجز</span>
                        </a>
                        <p data-translate="footer_description">
                            منصة شاملة لخدمات السفر للمصريين المسافرين للخارج
                        </p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h3 data-translate="quick_links">روابط سريعة</h3>
                    <ul class="footer-links">
                        <li><a href="services.html" data-translate="services">خدماتنا</a></li>
                        <li><a href="about.html" data-translate="about">من نحن</a></li>
                        <li><a href="contact.html" data-translate="contact">اتصل بنا</a></li>
                        <li><a href="dashboard.html" data-translate="dashboard">لوحة التحكم</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3 data-translate="contact_info">معلومات الاتصال</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> +20 ************</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> القاهرة، مصر</p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h3 data-translate="follow_us">تابعنا</h3>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 إنجز. <span data-translate="all_rights_reserved">جميع الحقوق محفوظة</span></p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="back-to-top" aria-label="العودة للأعلى">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- JavaScript Files -->
    <script src="../js/utils.js"></script>
    <script src="../js/language.js"></script>
    <script src="../js/main.js"></script>
    
    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
        
        // Services filter functionality
        const filterTabs = document.querySelectorAll('.filter-tab');
        const serviceCards = document.querySelectorAll('.service-card');
        
        filterTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                
                // Update active tab
                filterTabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // Filter services
                serviceCards.forEach(card => {
                    const category = card.getAttribute('data-category');
                    
                    if (filter === 'all' || category === filter) {
                        card.style.display = 'block';
                        card.classList.add('animate-fade-in');
                    } else {
                        card.style.display = 'none';
                        card.classList.remove('animate-fade-in');
                    }
                });
            });
        });
    </script>
</body>
</html>
